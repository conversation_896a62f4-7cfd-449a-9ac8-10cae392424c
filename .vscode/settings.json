{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll.eslint": "explicit"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.format.enable": true}