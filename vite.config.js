import path from "path";
import { defineConfig, loadEnv } from "vite";
import { createHtmlPlugin } from "vite-plugin-html";
import createVitePlugins from "./vite/plugins";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    // base: "/ztf-zixun/cfcec-frontend/dev/",
    plugins: [
      createVitePlugins(env, command === "build"),
      createHtmlPlugin({
        inject: {
          data: {
            ...env,
            injectScript: "<script type='module' src='./static.env.config.js'></script>"
          }
        }
      })
    ],
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src")
      },

      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    // vite 相关配置
    server: {
      port: 18011,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        [env.VITE_APP_BASE_API]: {
          // target: "http://devops.smartcloud.com:10082/ztf-zixun/c-park-aip-frontend/dev/prod-api",
          target: "http://devops.smartcloud.com:10082/ztf-zixun/jsyp-backend/dev/",
          //target: "http://************:9006",
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-api/, "")
        },
        "/ws": {
          // target: "http://devops.smartcloud.com:10082/ztf-zixun/c-park-aip-frontend/dev/prod-api",
          target: "ws://devops.smartcloud.com:10082/ztf-zixun/c-park-aip-frontend/dev",
          //target: "ws://************:9006",
          changeOrigin: true,
          rewrite: p => p.replace(/^\/dev-ws-api/, ""),
          ws: true
        }
      }
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: atRule => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    },
    optimizeDeps: {
      // include: ["@/components/vform/designer.umd.js"] //此处路径必须跟main.js中import路径完全一致！
    }
  };
});
