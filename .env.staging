
# 页面标题
VITE_APP_TITLE = 今世缘酒业智慧园区

# sso打包测试环境
VITE_APP_ENV = 'staging'

# sso打包测试环境
# VITE_APP_BASE_API = '/ztf-zixun/jsyp-frontend/dev/prod-api'
VITE_APP_BASE_API = '/jsyp-frontend-web-sso/prod-api'
# 应用访问路径 例如使用前缀 /admin/
# VITE_APP_CONTEXT_PATH = '/ztf-zixun/jsyp-frontend/dev/'
VITE_APP_CONTEXT_PATH = '/jsyp-frontend-web-sso/'
#地图路径
# VITE_APP_MAP = '/ztf-zixun/jsyp-frontend/dev/'
VITE_APP_MAP = '/jsyp-frontend-web-sso/'

# sso打包测试环境跳转传参url
VITE_APP_REDIRECT_URL = 'https://test-tx.jinshiyuan.com.cn'

#测试环境单点退出地址
VITE_APP_LOGOOUTURL = 'https://testtymh.jinshiyuan.com.cn'

# 测试环境客户端id
VITE_APP_CLIENTID = 'd2034373-fc2f-40c9-a9e7-0a203be2f125'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip