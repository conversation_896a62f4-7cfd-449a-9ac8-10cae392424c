# 页面标题
VITE_APP_TITLE = 今世缘酒业智慧通行

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'
# 开发环境配置
VITE_APP_ENV = 'development'

# 开发环境
VITE_APP_BASE_API = '/dev-api'
VITE_APP_BASE_WS_API = '/dev-ws-api'

# 环境类型配置 (jsyp: 今世缘环境, devcloud: 研发云环境)
VITE_APP_ENV_TYPE = 'devcloud'

# 今世缘环境登录配置
VITE_APP_JSY_ENV = 'https://jsyoa.jinshiyuan.com.cn/login?service=https%3A%2F%2Fjsyoa.jinshiyuan.com.cn%2F%3FintunifyauthTransferUrl%3D%252Fapi%252Fsso%252Foauth2.0%252Fprocess%253Fclient_id%253D01efabbb-ba14-40de-829d-bbd8dfc9d0d1%2526redirect_uri%253Dhttps%25253A%25252F%25252Ftx.jinshiyuan.com.cn%25252Fjsyp-frontend-web-sso%26tk%3Df14dd48e0f464433deeead96647104d6'

# 研发云环境登录配置
VITE_APP_DEVCLOUD_ENV = 'http://devops.smartcloud.com:10082/ztf-zixun/jsyp-frontend/dev/login?redirect=http://devops.smartcloud.com:10082/ztf-zixun/jsyp-fronted-big-screen/dev/index'
#地图路径
VITE_APP_MAP = '/dev-api'

# 接口加密功能开关(如需关闭 后端也必须对应关闭)
VITE_APP_ENCRYPT = true
# 接口加密传输 RSA 公钥与后端解密私钥对应 如更换需前后端一同更换
VITE_APP_RSA_PUBLIC_KEY = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='
# 接口响应解密 RSA 私钥与后端加密公钥对应 如更换需前后端一同更换
VITE_APP_RSA_PRIVATE_KEY = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE='
# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'
