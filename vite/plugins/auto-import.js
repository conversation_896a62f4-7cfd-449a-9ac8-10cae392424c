import autoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

export default function createAutoImport() {
  return autoImport({
    imports: ["vue", "vue-router", "pinia"],
    resolvers: [ElementPlusResolver()],
    dts: false,
    // dts: "src/auto-import.d.ts",
    eslintrc: {
      enabled: false
      // 1、改为true用于生成eslint配置。2、生成后改回false，避免重复生成消耗
    }
  });
}
