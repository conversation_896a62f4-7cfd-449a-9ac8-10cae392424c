import { AntDesignVueResolver, ElementPlusResolver } from "unplugin-vue-components/resolvers";
import components from "unplugin-vue-components/vite";
export default function createComponents() {
  return components({
    dirs: ["src/components/"],
    directoryAsNamespace: true,
    extensions: ["vue"],
    resolvers: [
      // 自动导入 Element Plus 组件
      ElementPlusResolver(),
      AntDesignVueResolver({ importStyle: false })
    ]
    // 配置文件生成位置
    // dts: "src/components.d.ts"
  });
}
