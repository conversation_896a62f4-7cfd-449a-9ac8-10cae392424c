@import "./variables.module.scss";
@import "@/assets/styles/screen.scss";

//登录页样式开始
.login {
  background-image: url("../images/background.png");
  background-size: cover;
  .loginBg {
    // width: 1080px;
    width: 42%;
    height: 100%;
    // background-image: url("../images/bg.jpg");
    background-size: cover;
  }
  .loginFormBox {
    .el-form {
      background-color: rgba(255, 255, 255, 0.3);
      padding: 5rem 6.25rem;
      width: 42rem !important;
      .el-form-item__label:before {
        color: #aa001e !important;
      }
    }
  }
  .login-form {
    .title {
      font-size: vw(40);
      color: #00192e;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .loginBtn {
      // background: linear-gradient(90deg, #4ec3ea 0%, #5174eb 100%);
      height: vw(60);
      box-shadow: 0px vw(3) vw(14) vw(1) rgba(6, 62, 110, 0.19);
      border-radius: vw(4);
      font-size: vw(28);
      width: 100%;
      &:hover {
        opacity: 0.8;
      }
    }
    .input-icon {
      margin-right: vw(11);
    }
  }
  .el-form-item {
    flex-direction: column;
    align-items: flex-start;
    .el-input {
      width: vw(467);
      box-shadow: 0px 0px vw(10) 1px rgba(56, 56, 56, 0.08);
      border-radius: vw(6);
      .el-input__wrapper {
        border: vw(1) solid #b7ecf0;
        box-shadow: none;
        padding: 0 vw(15);
        &.is-focus {
          border: vw(1) solid #409eff;
        }
      }
    }
    &.is-error {
      .el-input__wrapper {
        border: 1px solid #f56c6c;
        &.is-focus {
          border: 1px solid #f56c6c;
        }
      }
    }
  }
}
//登录页样式结束

//top样式开始
.navbar {
  .topmenu-container.el-menu--horizontal.menuItem {
    background-color: transparent;
    border-bottom: none !important;
    align-items: center;
    justify-content: center;
    height: 50px;
    .el-menu-item {
      color: #fff;
      float: none;
      height: 26px !important;
      border-bottom: none !important;
    }
  }
  /* 背景色隐藏 */
  .topmenu-container.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
  .topmenu-container.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
  .topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title:hover {
    background: linear-gradient(90deg, rgba(224, 195, 252, 0) 0%, rgba(142, 197, 252, 0.68) 100%) !important;
    border-radius: 3px 3px 3px 3px;
  }
  .topmenu-container.el-menu--horizontal > .el-menu-item.is-active,
  .el-menu--horizontal > .el-sub-menu.is-active .el-submenu__title {
    border-bottom: none !important;
    color: #fff !important;
    background: linear-gradient(90deg, rgba(224, 195, 252, 0.1) 0%, rgba(142, 197, 252, 0.68) 100%) !important;
    border-radius: 3px 3px 3px 3px;
    height: 26px;
  }
}

//top样式结束

//侧边栏样式开始
.sidebar-container {
  .sideBarMenuBox {
    background: #fff;
    .sideBarMenu {
      background: #fff;
      .el-menu-item,
      .el-sub-menu {
        .menu-title {
          color: black;
        }
        // &:hover {
        //   background: #f4f5ff !important;
        //   // border-right: 4px solid #2767ce;
        // }
      }
    }

    &.theme-dark {
      .is-active {
        & > .el-sub-menu__title {
          color: #409eff !important;
        }
      }
    }
  }
  // & .theme-dark .is-active > .el-sub-menu__title {
  //   color: #409eff !important;
  // }
}
#app {
  .sidebar-container {
    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: #f4f5ff !important;
      }
    }
    & .theme-dark .is-active > .el-sub-menu__title {
      color: #409eff !important;
      .menu-title {
        color: #409eff !important;
      }
    }
    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      &:hover {
        background-color: #f4f5ff !important;
      }
    }
    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: #fff !important;

      &:hover {
        background-color: #f4f5ff !important;
      }
    }
    .el-menu-item.is-active,
    & .theme-dark .el-sub-menu .el-menu-item.is-active {
      background: #f4f5ff !important;
      border-right: 4px solid #2767ce;
    }
  }
}
.el-menu--vertical {
  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: #f4f5ff !important;
    }
  }
}
// #app .sidebar-container .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
// #app .sidebar-container .theme-dark .el-sub-menu .el-menu-item {
//   background-color: #fff !important;
//   &:hover {
//     background: #f4f5ff !important;
//   }
// }
// #app .sidebar-container .theme-dark .is-active > .el-sub-menu__title {
//   &:hover {
//     background: #f4f5ff !important;
//   }
// }
// #app .sidebar-container .theme-dark .el-sub-menu .el-menu-item.is-active {
//   background: #f4f5ff !important;
//   border-right: 4px solid #2767ce;
// }
//侧边栏样式结束

// background: #f5f5f5;
.formCardContainer {
  border-radius: 6px;
  .pagination-container {
    height: 32px;
    .el-pagination {
      margin: 0;
    }
  }
  .el-card__body {
    padding: 18px 20px 20px 20px !important;
  }
  .menuTreeBox {
    height: calc(100vh - 208px);
    .video-tree {
      height: calc(100% - 104px);
    }
  }
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      background-color: rgba(0, 0, 0, 0.02) !important;
      color: rgba(0, 0, 0, 0.88);
    }
  }
}
.el-button--primary:not(.is-plain, .is-text, .is-link) {
  background-color: $--custom-primary-color;
  border-color: $--custom-primary-color;
  &:hover {
    background-color: $--custom-primary-hover;
    border-color: $--custom-primary-hover;
  }
}

//表格页面样式结束

//全局样式开始
.el-dialog {
  border-radius: 8px !important;
  box-shadow:
    0px 12px 48px 16px rgba(0, 0, 0, 0.03),
    0px 9px 28px 0px rgba(0, 0, 0, 0.05),
    0px 6px 16px -8px rgba(0, 0, 0, 0.08);
  .el-button--primary:not(.is-plain, .is-text, .is-link) {
    background-color: $--custom-primary-color;
    border-color: $--custom-primary-color;
    &:hover {
      background-color: $--custom-primary-hover;
      border-color: $--custom-primary-hover;
    }
  }
}
.el-message-box {
  border-radius: 8px !important;
  box-shadow:
    0px 12px 48px 16px rgba(0, 0, 0, 0.03),
    0px 9px 28px 0px rgba(0, 0, 0, 0.05),
    0px 6px 16px -8px rgba(0, 0, 0, 0.08);
  padding-bottom: 20px;
  .el-button--primary:not(.is-plain, .is-text, .is-link) {
    background-color: $--custom-primary-color;
    border-color: $--custom-primary-color;
    &:hover {
      background-color: $--custom-primary-hover;
      border-color: $--custom-primary-hover;
    }
  }
}
.customModal {
  .pagination-container {
    height: 32px;
    position: relative !important;
    .el-pagination {
      margin: 0;
    }
  }
}
// 树分割线
.division {
  width: 1px;
  background: #d9d9d9;
}

.svg-icon.taskTopIcon {
  font-size: 12px;
  color: #ff4d4f;
  vertical-align: top;
  margin-right: 2px;
}

//弹窗内容居中
.dialogBodyCenter {
  .el-dialog__body {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.avatar-icon-box {
  width: 100%;
  .viewerBoxFile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img,
    video {
      width: 90%;
      max-height: 150px;
    }
  }
}

//全局样式结束

//基本信息表单样式开始

.basicInfoForm {
  .el-item-header {
    height: 48px;
    width: 100%;
    padding-left: 20px;
    margin: 0 0 20px 0;
    // border-bottom: 1px solid #d8dce5;
    background: linear-gradient(to right, #ecf3fe, #f2fafe);
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.12),
      0 0 3px 0 rgba(0, 0, 0, 0.04);
    div {
      padding: 0 20px;
      height: 100%;
      width: fit-content;
      line-height: 48px;
      font-size: 16px;
    }
    .handleBox {
      align-items: center;
      padding: 0;
      width: 100%;
      justify-content: space-between;
    }
    .headerIcon {
      .iconStyle {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
//基本信息表单样式结束

//左侧树样式开始
.taskTreeTitle {
  .treeTop {
    font-weight: 700;
    text-align: center;
  }
  .treeInfo {
    font-size: 13px;
  }
}
//左侧树样式结束
