@use "sass:math";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
$hederButtoncolor: #f2f2f2;
$titleColor: #a5d8fc;
$datainfoColor: #e4f3ff;
$vw12: vw(12);
$vw14: vw(14);
$vw16: vw(16);
$titleHeight: 5vh;
/** 基础通用 **/
.pt5 {
  padding-top: vw(5);
}
.pr5 {
  padding-right: vw(5);
}
.pb5 {
  padding-bottom: vw(5);
}
.pl5 {
  padding-left: vw(5);
}
.pr10 {
  padding-right: vw(10);
}
.pt10 {
  padding-top: vw(10);
}

.pb10 {
  padding-bottom: vw(10);
}
.pl10 {
  padding-left: vw(10);
}
.mt5 {
  margin-top: vw(5);
}
.mr5 {
  margin-right: vw(5);
}
.mb5 {
  margin-bottom: vw(5);
}
.mb8 {
  margin-bottom: vw(8);
}
.ml5 {
  margin-left: vw(5);
}
.mt10 {
  margin-top: vw(10);
}
.mr10 {
  margin-right: vw(10);
}
.mb10 {
  margin-bottom: vw(10);
}
.ml10 {
  margin-left: vw(10);
}
.mt20 {
  margin-top: vw(20);
}
.mt18 {
  margin-top: vw(18);
}
.mr20 {
  margin-right: vw(20);
}
.mb18 {
  margin-bottom: vw(18);
}
.mb20 {
  margin-bottom: vw(20);
}
.ml20 {
  margin-left: vw(20);
}

.container_box {
  width: 100vw;
  height: 100vh;
  position: relative;

  overflow: hidden;
}
.set-model-color {
  color: $hederButtoncolor;
  border-color: $hederButtoncolor;
}
.bigScreen-model-back-color {
  background: linear-gradient(-90deg, rgba(10, 19, 28, 0.9) 0%, rgba(15, 28, 40, 0.7) 50%, rgba(16, 28, 40, 0.3) 80%, rgba(16, 29, 41, 0) 100%);
}
.bigScreen-model-back-color-left {
  background: linear-gradient(88.9587deg, rgba(10, 19, 28, 0.9) 0%, rgba(15, 28, 40, 0.7) 50%, rgba(16, 28, 40, 0.3) 80%, rgba(16, 29, 41, 0) 100%);
}
.set-model-fontB {
  font-weight: bold;
}
/*--------------------------------------------------------------------------------------------*/
:deep(.el-form-item__label) {
  // filter: brightness(0.7);
  // color: $datainfoColor;
  font-size: $vw14;
}
.el-button {
  font-size: $vw14;
}
:deep(.el-tag__content) {
  font-size: $vw12;
}
:deep(.el-tag--small) {
  height: vw(20);
}
:deep(.el-input--small) {
  font-size: $vw12;
}
.el-pagination {
  font-size: $vw14;
}
.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.custom-dialog-header .close-button {
  font-size: vw(16);
}

/*设置文字字体颜色渐变*/
.set_font_colorliner {
  background: linear-gradient(0deg, #a5d8fc 0%, #ffffff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.setEllipsis {
  white-space: wrap;
}
.beforImg {
  border: 0.15vw solid;
  border-radius: 0.3vw 0 0 0.3vw;
  padding-left: 0.3vw;
  align-items: center;
}
.afterbutton {
  padding: 0.3vw;
  border-left: 0.15vw solid;
  margin-left: 0.3vw;
}

.every-model {
  border: 2px solid;

  padding: vh(10);
}

.titleEveryColor {
  color: $hederButtoncolor;
  border-color: $hederButtoncolor;
}

.highlight {
  transition: transform 0.3s ease; /* 动画持续时间和效果 */
  transform: scale(1.1);
  filter: brightness(1);
}
.nomalLight {
  filter: brightness(0.7);
  transform: scale(1);
}
.animate_cursor {
  transition: transform 0.3s ease; /* 动画持续时间和效果 */
  transform: scale(1);
}
.animate_cursor:hover {
  transform: scale(1.1);
  filter: brightness(1);
}
.containBlock {
  background: radial-gradient(circle, rgba(59, 80, 124, 0.7), rgba(0, 0, 0, 0.35));
  // border: 1px solid #ffffff;
  border-radius: 0.4vh;
  font-size: $vw16;
  color: #fff;
  text-align: center;

  padding: 0.5vh 0.5vw;
}
.leftmodel {
  left: vw(22);
  width: vw(320);
  top: vh(100);
}
$tranRight: calc(vw(503) - 1vw);
.transformRight {
  transform: translateX(-$tranRight);
  transition: transform 0.5s ease-in-out;
}
.transformReverse {
  transition: transform 0.5s ease-in-out;
}
.rightmodel {
  right: vw(22);

  width: vw(320);
  top: vh(100);
}
.transformLeft {
  transform: translateX($tranRight);
  transition: transform 0.5s ease-in-out;
}
$tranRightOniy: calc(vw(820));
.rightmodelOnly {
  width: vw(820);
  right: 0;
  height: 100vh;
}
.transformLeftOnly {
  transform: translateX($tranRightOniy);
  transition: transform 0.5s ease-in-out;
}
.setButtonLeft {
  top: 50vh;
  right: 0;
}
.setButtonRight {
  top: 50vh;
  left: 0;
  transform: rotate(180deg);
}
.setButtonRightOnly {
  top: 50vh;
  left: -1vw;
  transform: rotate(180deg);
}
.innerStyle {
  margin-top: vh(100);

  padding-right: vw(19);
}
.curStyleTitle {
  font-weight: normal;
  font-size: vw(20);
  color: $titleColor;
}
.dataTitleStyle {
  color: $datainfoColor;
  font-size: $vw12;
  margin: 0.5vh;
  opacity: 0.6;
}
.dataNumStyle {
  height: vh(32);
  line-height: vh(32);
  min-width: 7vw;
  background-color: rgba(24, 39, 50, 0.6);
  position: relative;
  transform: skewX(-30deg);
  padding-left: vw(10);
}

.curStyleNum {
  font-size: vh(30);
  width: vw(132);
  height: vh(42);
  background-color: rgba(24, 39, 50, 0.6);
  position: relative;
  transform: skewX(-20deg) translateX(vw(10));
  padding-left: vw(10);
}
.chunkStyle {
  background-color: rgba(19, 28, 38, 0.5);
  border-radius: vw(14);
  padding: 1vh 1vw;
}
.infoRemind_body {
  color: #fff;
  font-size: $vw16;

  .content {
    .content_title {
      color: $titleColor;
      font-size: vw(20);
    }
    .bodyWidth {
      color: $datainfoColor;
      width: 15vw;
      font-size: $vw16;
      margin-top: 1vh;
    }
    .promIcon {
      color: $datainfoColor;
      text-align: right;
      font-size: $vw16;
      margin-top: 1vh;
    }
  }
}
.showAnimal {
  animation: showHeight 1s ease-in-out;
}
@keyframes showHeight {
  from {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  to {
    opacity: 1;
  }
}
.fade-in-tr {
  animation: fade-in-tr 1s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}
@keyframes fade-in-tr {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.midmodel {
  top: 12vh;
  left: calc(25% + 1vw);
  width: calc(50% - 7vw);
}
.text_shadow_blue {
  background: linear-gradient(0deg, #a5d8fc 0%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text_gradient_color_primary {
  background: linear-gradient(0deg, #a5d8fc 0%, #ffffff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text_gradient_color_error {
  background: linear-gradient(0deg, #eb6969 0%, #e8a9ac 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text_gradient_color_success {
  background: linear-gradient(0deg, #a7e193 0%, #e7fbe0 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
