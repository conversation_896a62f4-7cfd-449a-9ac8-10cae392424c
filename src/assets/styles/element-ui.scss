// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}
.el-button {
  border-radius: 6px !important;
}
.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}
/*树横向滚动条样式*/
.video-tree {
  overflow: auto;
  height: calc(100vh - 248px);
  > .el-tree {
    > .el-tree-node {
      min-width: 100%;
      display: inline-block;
    }
  }
}
/*树横向滚动条样式*/
.left-tree {
  overflow: auto;
  max-height: calc(100vh - 248px);
  > .el-tree {
    > .el-tree-node {
      min-width: 100%;
      display: inline-block;
    }
  }
}
/*树组件上alert样式*/
.alertCheck {
  .el-alert__content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .el-alert__title {
      width: 70%;
    }
    .el-alert__description {
      margin: 0 0 0 4px;
      width: 25%;
    }
  }
}
.addFooterInItem {
  text-align: right;
  padding-left: 20px;
  padding-top: 10px;
  box-sizing: border-box;
}
.addFooter {
  display: flex !important;
  align-items: center;
  justify-content: flex-end;
}
/**
    必填项提示*
 */
.required {
  .el-form-item__label::before {
    content: "*";
    display: inline-block;
    margin-right: 4px;
    line-height: 32px;
    font-family:
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;
    font-size: 14px;
    color: #ff4949;
  }
}

//修改el-drawer边距
.el-drawer__header {
  margin-bottom: 12px !important;
}
.el-drawer__body {
  padding: unset !important;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
