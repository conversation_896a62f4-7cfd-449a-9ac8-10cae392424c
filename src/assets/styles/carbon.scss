$topHeight: 17vh; /* 最上层div内容高度 */
$topDataHeight: 6vh; /* 最上层div图标层高度 */
$midHeight: 20vh; /* 中间层div内容高度 */
$btmHeight: 25vh; /* 最下层div内容高度 */
$intervalHeight: 1.5vh; /* 内容块间隔 */
$titleHeight: 5vh; /* 内容块间隔 */
$selectHeight: 5vh; /* 选择器块高度 */
$moreHeight: calc($topHeight + $topDataHeight + $midHeight + $intervalHeight + $titleHeight); /* 详情内容高度 */
$nullHeight: calc($topHeight + $topDataHeight + $midHeight + $intervalHeight + $titleHeight * 2); /* nulldiv内容高度 */
$moreSelectHeight: 5vh; /* 选择器块高度 */
$moreContentHeight: calc($moreHeight - $moreSelectHeight); /* 详情内容除去选择器高度 */

$normalFont: 0.8vmax;
$bigFont: 1vmax;
$smallFont: 0.6vmax;
@use "sass:math";
$designWidth: 1920;
$designHeight: 1080;
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}
$hederButtoncolor: #f2f2f2;
$titleColor: #a5d8fc;
$datainfoColor: #e4f3ff;
.carbon {
  ::v-deep .el-date-editor {
  }
  .gradient-font1 {
    background: -webkit-linear-gradient(top, rgba(192, 227, 252, 1), rgba(230, 243, 252, 1));
    background: -o-linear-gradient(bottom, rgba(192, 227, 252, 1), rgba(230, 243, 252, 1));
    background: -moz-linear-gradient(bottom, rgba(192, 227, 252, 1), rgba(230, 243, 252, 1));
    background: linear-gradient(to bottom, rgba(192, 227, 252, 1), rgba(230, 243, 252, 1));
    -webkit-background-clip: text;
    color: transparent;
    font-weight: bold;
  }
  .gradient-font2 {
    background: -webkit-linear-gradient(top, rgba(103, 174, 224, 1), rgba(157, 196, 224, 1));
    background: -o-linear-gradient(bottom, rgba(103, 174, 224, 1), rgba(157, 196, 224, 1));
    background: -moz-linear-gradient(bottom, rgba(103, 174, 224, 1), rgba(157, 196, 224, 1));
    background: linear-gradient(to bottom, rgba(103, 174, 224, 1), rgba(157, 196, 224, 1));
    -webkit-background-clip: text;
    color: transparent;
    font-weight: bold;
  }
  .null {
    height: $nullHeight;
    z-index: 1;
    margin-bottom: $intervalHeight;
  }
  .blockbg {
    background-color: rgba(16, 28, 40, 0.4);
  }
  .blocksink {
    width: 100%;
    position: absolute;
    top: 56vh;
    left: 0;
  }
  .block {
    border-radius: 1vh;
    margin-bottom: $intervalHeight;
    .sp-num {
      padding-left: 1vw;
      padding-right: 1vw;
      font-size: $bigFont;
    }
    &-title {
      height: $titleHeight;
      line-height: $titleHeight;
      position: relative;
      background: url("@/assets/images/carbon/title1_copy.png") no-repeat;
      background-size: 95% 80%;
      background-position: center;
      font-weight: normal;
      font-size: vw(20);
      color: $titleColor;
      z-index: 10;
      &-font {
        position: absolute;
        top: -0.5vh;
        left: vw(37);
      }
      &-ico {
        font-size: $bigFont;
        position: absolute;
        top: -0.5vh;
        right: 1vw;
        color: #b3e2f0 !important;
        cursor: pointer;
      }
    }
    &-titlesp {
      background: url("@/assets/images/carbon/title1_copy.png") no-repeat !important;
      background-size: 95% 80% !important;
      background-position: center !important;
    }
    &-body {
      position: relative;
      .body-datas {
        height: $topDataHeight;
        line-height: $topDataHeight;
        color: #fff;
        font-size: $normalFont;
        font-weight: bold;
        text-indent: 5vw;
        background: url("@/assets/images/carbon/7.png") no-repeat;
        background-position: 5% center;
        background-size: contain;
      }
      .body-datasp {
        display: flex;
        height: $topDataHeight;
        line-height: $topDataHeight;
        color: #fff;
        font-size: $normalFont;
        font-weight: bold;
        .dItem {
          width: 50%;
          height: 100%;
          clear: both;
          &-fs {
            font-size: $bigFont;
          }
          &-ut {
            font-size: $smallFont;
            color: #fff;
            padding-left: 5px;
          }
          &-lt {
            width: 30%;
            height: 100%;
            float: left;
            background: url("@/assets/images/carbon/7.png") no-repeat;
            background-position: center center;
            background-size: contain;
          }
          &-rt {
            width: 70%;
            height: 100%;
            float: left;
            &-top {
              width: 100%;
              height: 50%;
              font-size: $smallFont;
              line-height: calc($bigFont * 2);
            }
            &-btm {
              width: 100%;
              height: 50%;
              line-height: $normalFont;
            }
          }
        }
      }
      .body-ets {
        height: $topHeight;
        z-index: 2;
      }
      .body-bgs {
        width: 100%;
        height: $topHeight;
        position: absolute;
        top: 50%;
        right: 0;
        background: url("@/assets/images/carbon/pie.png") no-repeat;
        background-position: 0% 0%;
        background-size: 50% 50%;
        z-index: 3;
      }
      .body-etsp {
        height: $midHeight !important;
        z-index: 2;
      }
      .body-etmax {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        height: $btmHeight !important;
        z-index: 2;
        @for $i from 1 through 3 {
          .sink#{$i} {
            background: url("@/assets/images/carbon/#{$i}.png") no-repeat;
            background-position: 45% 0%;
            background-size: contain;
          }
        }
        .sinkItem {
          width: 100%;
          height: 6vh;
          line-height: 6vh;
          margin-top: 1vh;
          margin-bottom: 1vh;
          display: flex;
          align-items: center;
          &-lt {
            width: 25%;
            height: 100%;
            text-align: right;
            font-size: $normalFont;
          }
          &-mid {
            width: 35%;
            height: 100%;
            text-indent: 20%;
          }
          &-rt {
            width: auto;
            height: 100%;
            font-size: $normalFont;
            color: #fff;
          }
          &-no {
            padding-left: 0.5vw;
            padding-right: 0.5vw;
          }
          &-ns {
            font-size: $bigFont;
            font-weight: bold;
          }
          &-nt {
            font-size: $normalFont;
            padding-left: 0.5vw;
          }
          &-pt {
            font-size: $normalFont;
            color: #fff;
            padding-left: 0.5vw;
          }
          &-green {
            font-size: $normalFont;
            font-weight: bold;
            color: #64db40;
          }
          &-red {
            font-size: $normalFont;
            font-weight: bold;
            color: #ff3f0f;
          }
        }
      }
      .body-etmore {
        position: relative;
        height: $moreHeight !important;
        z-index: 2;
        .etmore-param {
          height: $moreSelectHeight;
          line-height: $moreSelectHeight;
          text-align: right;
          padding-right: 1vw;
        }
        .etmore-content {
          height: $moreContentHeight;
        }
        .etmore-contentsp {
          height: $moreHeight;
        }
        .etmore-bgs {
          width: 100%;
          height: $moreContentHeight;
          position: absolute;
          top: 50%;
          right: 0;
          background: url("@/assets/images/carbon/pie.png") no-repeat;
          background-position: 50% -30%;
          background-size: 70% 50%;
          z-index: 3;
        }
        .etmore-flex {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          overflow: auto;
          &-item {
            width: 33%;
            position: relative;
          }
          .item-big {
            height: $moreContentHeight;
          }
          .item-small {
            height: calc($moreContentHeight / 2);
          }
        }
      }
    }
  }
}
