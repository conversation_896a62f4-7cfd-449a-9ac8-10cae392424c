import { createApp } from "vue";

import Cookies from "js-cookie";

import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import locale from "element-plus/es/locale/lang/zh-cn";

import "@/assets/styles/index.scss"; // global css

import VueViewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import App from "./App";
import directive from "./directive"; // directive
import router from "./router";
import store from "./store";

// 注册指令
import { download } from "@/utils/request";
import plugins from "./plugins"; // plugins
// 预设动画
import animate from "./animate";
// svg图标
import SvgIcon from "@/components/SvgIcon";
import elementIcons from "@/components/SvgIcon/svgicon";
import "virtual:svg-icons-register";

import "./permission"; // permission control

import { getConfigKey, updateConfigByKey } from "@/api/system/config";

import Pagination from "@/components/Pagination";
import checkRule from "@/utils/checkRule";
import { toRem, toSclePx } from "@/utils/px2Rem";
import { addDateRange, handleTree, parseTime, requireImg, resetForm, selectDictLabel, selectDictLabels } from "@/utils/ruoyi";
import hljsVuePlugin from "@highlightjs/vue-plugin";
import "highlight.js/lib/common";
import hljs from "highlight.js/lib/core";
import "highlight.js/styles/atom-one-dark.css";

// 分页组件
// 自定义表格工具组件
//import RightToolbar from "@/components/RightToolbar";
// 富文本组件
//import Editor from "@/components/Editor";
// 文件上传组件
//import FileUpload from "@/components/FileUpload";
// 图片上传组件
//import ImageUpload from "@/components/ImageUpload";
// 图片预览组件
//import ImagePreview from "@/components/ImagePreview";
// 自定义树选择组件
//import TreeSelect from "@/components/TreeSelect";
// 字典标签组件
//import DictTag from "@/components/DictTag";

// Vue3表单组件
//import "@/components/vform/designer.style.css";
//import VForm3 from "@/components/vform/designer.umd.js";

const app = createApp(App);
// 全局绑定枚举值过滤 - 根据值返回名称
app.config.globalProperties.$filterEnumName = (enums, key) => {
  let e = enums.filter(item => {
    return key == item.key;
  });
  if (e && e.length > 0) {
    return e[0].value;
  }
  return "";
};

// // 全局绑定枚举值过滤 - 根据值返回key
app.config.globalProperties.$filterEnumKey = (enums, value) => {
  let e = enums.filter(item => {
    return value == item.value;
  });
  if (e && e.length > 0) {
    return e[0].key;
  }
  return "";
};
app.directive("highlight", function (el) {
  let highlight = el.querySelectorAll("pre code");
  highlight.forEach(block => {
    // Deprecated as of 10.7.0. highlightBlock will be removed entirely in v12.0
    // Deprecated as of 10.7.0. Please use highlightElement now.
    hljs.highlightElement(block);
  });
});

// 引入代码高亮，并进行全局注册
app.use(hljsVuePlugin);

// 全局方法挂载

app.config.globalProperties.getConfigKey = getConfigKey;
app.config.globalProperties.updateConfigByKey = updateConfigByKey;

app.config.globalProperties.download = download;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addDateRange = addDateRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.selectDictLabels = selectDictLabels;
app.config.globalProperties.animate = animate;
app.config.globalProperties.requireImg = requireImg;
app.config.globalProperties.toRem = toRem;
app.config.globalProperties.toSclePx = toSclePx;
// 全局组件挂载
//app.component("DictTag", DictTag);
app.component("Pagination", Pagination);
//app.component("TreeSelect", TreeSelect);
//app.component("FileUpload", FileUpload);
//app.component("ImageUpload", ImageUpload);
//app.component("ImagePreview", ImagePreview);
//app.component("RightToolbar", RightToolbar);
//app.component("Editor", Editor);

app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.component("svg-icon", SvgIcon);
app.use(checkRule);
// 全局注册VForm3，同时注册了v-form-designer、v-form-render等组件
//app.use(VForm3);
app.use(VueViewer, {
  defaultOptions: {
    // 自定义默认配置
    zIndex: 3000,
    inline: false, // Default: false. Enable inline mode.
    button: true, // Show the button on the top-right of the viewer.
    navbar: true, // Specify the visibility of the navbar.
    title: false, // Specify the visibility and the content of the title.
    toolbar: false, // Specify the visibility and layout of the toolbar its buttons.
    tooltip: true, // Show the tooltip with image ratio (percentage) when zooming in or zooming out.
    movable: true, // Enable to move the image.
    zoomable: true, // Enable to zoom the image.
    rotatable: false, // Enable to rotate the image.
    scalable: true, // Enable to scale the image.
    transition: true, // Enable CSS3 Transition for some special elements.
    fullscreen: false, // Enable to request full screen when play.
    keyboard: true, // Enable keyboard support.
    url: "src" // Default: 'src'. Define where to get the original image URL for viewing.
  }
});
directive(app);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get("size") || "default"
});

app.mount("#app");
