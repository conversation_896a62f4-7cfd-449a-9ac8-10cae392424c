/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Breadcrumb: typeof import('./components/Breadcrumb/index.vue')['default']
    Crontab: typeof import('./components/Crontab/index.vue')['default']
    CrontabDay: typeof import('./components/Crontab/day.vue')['default']
    CrontabHour: typeof import('./components/Crontab/hour.vue')['default']
    CrontabMin: typeof import('./components/Crontab/min.vue')['default']
    CrontabMonth: typeof import('./components/Crontab/month.vue')['default']
    CrontabResult: typeof import('./components/Crontab/result.vue')['default']
    CrontabSecond: typeof import('./components/Crontab/second.vue')['default']
    CrontabWeek: typeof import('./components/Crontab/week.vue')['default']
    CrontabYear: typeof import('./components/Crontab/year.vue')['default']
    DeviceDeviceList: typeof import('./components/Device/deviceList/index.vue')['default']
    DeviceDeviceListDeviceListDialog: typeof import('./components/Device/deviceList/deviceListDialog.vue')['default']
    DeviceDeviceListEditDevice: typeof import('./components/Device/deviceList/editDevice.vue')['default']
    DeviceDeviceListFileUpload: typeof import('./components/Device/deviceList/fileUpload.vue')['default']
    DeviceDeviceTypes: typeof import('./components/Device/deviceTypes/index.vue')['default']
    DeviceDeviceTypesDeviceTypeDialog: typeof import('./components/Device/deviceTypes/deviceTypeDialog.vue')['default']
    DeviceLayerManager: typeof import('./components/Device/LayerManager/index.vue')['default']
    DeviceLayerManagerComponentDetailDialog: typeof import('./components/Device/LayerManager/component/DetailDialog.vue')['default']
    DeviceLayerManagerComponentTable: typeof import('./components/Device/LayerManager/component/Table.vue')['default']
    DeviceSetArea: typeof import('./components/Device/SetArea/index.vue')['default']
    DeviceSetAreaComponentsAreaForm: typeof import('./components/Device/SetArea/components/areaForm.vue')['default']
    DeviceSetAreaComponentsDragAddForm: typeof import('./components/Device/SetArea/components/dragAddForm.vue')['default']
    DeviceSetAreaComponentsRightPanel: typeof import('./components/Device/SetArea/components/rightPanel.vue')['default']
    DictTag: typeof import('./components/DictTag/index.vue')['default']
    Editor: typeof import('./components/Editor/index.vue')['default']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FileUpload: typeof import('./components/FileUpload/index.vue')['default']
    FlowElInputTag: typeof import('./components/flow/ElInputTag/index.vue')['default']
    FlowExpression: typeof import('./components/flow/Expression/index.vue')['default']
    FlowRole: typeof import('./components/flow/Role/index.vue')['default']
    FlowUser: typeof import('./components/flow/User/index.vue')['default']
    Hamburger: typeof import('./components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./components/HeaderSearch/index.vue')['default']
    IconSelect: typeof import('./components/IconSelect/index.vue')['default']
    IFrame: typeof import('./components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./components/ImageUpload/index.vue')['default']
    LeftTreeTable: typeof import('./components/leftTreeTable/index.vue')['default']
    LeftTreeTableHeader: typeof import('./components/leftTreeTable/header.vue')['default']
    LeftTreeTableHeaderScope: typeof import('./components/leftTreeTable/headerScope.vue')['default']
    LeftTreeTableTree: typeof import('./components/leftTreeTable/tree.vue')['default']
    MainFlex: typeof import('./components/mainFlex/index.vue')['default']
    MainFlexLeft: typeof import('./components/mainFlex/left.vue')['default']
    MainFlexRight: typeof import('./components/mainFlex/right.vue')['default']
    MainFlexTopDown: typeof import('./components/mainFlex/topDown.vue')['default']
    MapDrawerMap: typeof import('./components/mapDrawer/map.vue')['default']
    MapDrawerMapDrawer: typeof import('./components/mapDrawer/mapDrawer.vue')['default']
    Pagination: typeof import('./components/Pagination/index.vue')['default']
    ParentView: typeof import('./components/ParentView/index.vue')['default']
    Process: typeof import('./components/Process/index.vue')['default']
    ProcessDesigner: typeof import('./components/Process/designer.vue')['default']
    ProcessPanelCommonPanel: typeof import('./components/Process/panel/commonPanel.vue')['default']
    ProcessPanelConditionPanel: typeof import('./components/Process/panel/conditionPanel.vue')['default']
    ProcessPanelExecutionListener: typeof import('./components/Process/panel/executionListener.vue')['default']
    ProcessPanelFormPanel: typeof import('./components/Process/panel/formPanel.vue')['default']
    ProcessPanelMultiInstance: typeof import('./components/Process/panel/multiInstance.vue')['default']
    ProcessPanelOtherPanel: typeof import('./components/Process/panel/otherPanel.vue')['default']
    ProcessPanelTaskListener: typeof import('./components/Process/panel/taskListener.vue')['default']
    ProcessPanelTaskPanel: typeof import('./components/Process/panel/taskPanel.vue')['default']
    ProcessViewer: typeof import('./components/Process/viewer/index.vue')['default']
    RightToolbar: typeof import('./components/RightToolbar/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuoYiDoc: typeof import('./components/RuoYi/Doc/index.vue')['default']
    RuoYiGit: typeof import('./components/RuoYi/Git/index.vue')['default']
    Screenfull: typeof import('./components/Screenfull/index.vue')['default']
    SizeSelect: typeof import('./components/SizeSelect/index.vue')['default']
    SvgIcon: typeof import('./components/SvgIcon/index.vue')['default']
    TableIcon: typeof import('./components/tableIcon/index.vue')['default']
    TopNav: typeof import('./components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./components/TreeSelect/index.vue')['default']
    UserTable: typeof import('./components/userTable/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
