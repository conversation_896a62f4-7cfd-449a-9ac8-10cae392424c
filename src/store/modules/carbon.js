import {
  carbon_getBuildingTypeEnums,
  carbon_getDataMergeMethodEnums,
  carbon_getEmissionUnitList,
  carbon_getEmissionUnitListAll,
  carbon_getUnitTypeEnums,
  carbon_queryDataSourceEnums,
  carbon_queryDeviceEnergyCategoryEnums,
  carbon_queryEmissionsEnums,
  carbon_queryEnergyCategoryEnums,
  carbon_queryEnergyConsumptionTypeEnums,
  carbon_queryEnergyTypeEnums,
  carbon_queryMeterAllTypeEnums,
  carbon_queryMeterEnergyCategoryEnums,
  carbon_queryMeterTableTypeEnums,
  carbon_queryMeterTypeEnums
} from "@/api/carbon/index.js";
const useCarbonStore = defineStore("carbon", {
  state: () => ({
    // 排放单元下拉列表
    emissionUnitList: [],

    // 排放单元下拉列表，全部
    emissionUnitListAll: [],

    // 建筑类型
    buildingTypeEnums: [],

    // 设备核算边界
    deviceEnergyCategoryEnums: [],

    // 能源类别
    energyCategoryEnums: [],

    // 数据来源
    dataSourceEnums: [],

    // 能耗分项
    energyTypeEnums: [],

    // 仪表核算边界
    meterEnergyCategoryEnums: [],

    // 仪表类型
    meterTypeEnums: [],

    // 全部仪表类型
    meterAllTypeEnums: [],

    // 仪表总分表
    meterTableTypeEnums: [],

    // 电表用途
    energyConsumptionTypeEnums: [],

    // 排放单元类型
    unitTypeEnums: [],

    // 排放数据合并方法
    dataMergeMethodEnums: [],

    // 排放类型
    emissionsEnums: []
  }),
  actions: {
    // 查询排放单元下拉列表
    getEmissionUnitList(data) {
      return new Promise((resolve, reject) => {
        carbon_getEmissionUnitList(data)
          .then(res => {
            if (res.code === 200) {
              this.emissionUnitList = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询排放单元下拉列表，所有
    getEmissionUnitListAll(data) {
      return new Promise((resolve, reject) => {
        carbon_getEmissionUnitListAll(data)
          .then(res => {
            if (res.code === 200) {
              this.emissionUnitListAll = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询建筑类型
    getBuildingTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_getBuildingTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.buildingTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询设备核算边界枚举值
    getDeviceEnergyCategoryEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryDeviceEnergyCategoryEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.deviceEnergyCategoryEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询能源类别枚举值
    getEnergyCategoryEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryEnergyCategoryEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.energyCategoryEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询数据来源枚举值
    getDataSourceEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryDataSourceEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.dataSourceEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询能耗分项枚举值
    getEnergyTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryEnergyTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.energyTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询仪表核算边界枚举值
    getMeterEnergyCategoryEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryMeterEnergyCategoryEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.meterEnergyCategoryEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询仪表类型枚举值
    getMeterTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryMeterTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.meterTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询全部仪表类型枚举值
    getMeterAllTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryMeterAllTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.meterAllTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询仪表总分表枚举值
    getMeterTableTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryMeterTableTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.meterTableTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询电表用途枚举值
    getEnergyConsumptionTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_queryEnergyConsumptionTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.energyConsumptionTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询排放单元类型枚举值
    getUnitTypeEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_getUnitTypeEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.unitTypeEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询排放数据合并方法枚举值
    getDataMergeMethodEnums(data) {
      return new Promise((resolve, reject) => {
        carbon_getDataMergeMethodEnums(data)
          .then(res => {
            if (res.code === 200) {
              this.dataMergeMethodEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 查询排放类型枚举值
    getEmissionsEnums({ commit }, data) {
      return new Promise((resolve, reject) => {
        carbon_queryEmissionsEnums(data)
          .then(res => {
            if (res.status === 200) {
              this.emissionsEnums = res.data || [];
            }
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export default useCarbonStore;
