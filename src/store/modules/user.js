import { getInfo, login, logout } from "@/api/login";
import { getUnreadNum } from "@/api/system/notice";
import defAva from "@/assets/images/profile.jpg";
import { getToken, removeToken, setToken } from "@/utils/auth";

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    id: "",
    name: "",
    avatar: "",
    roles: [],
    permissions: [],
    backLogNum: 0
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then(res => {
            setToken(res.data.token);
            this.token = res.data.token;
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            const user = res.data.user;
            const avatar = user.avatar == "" || user.avatar == null ? defAva : user.avatar;

            if (res.data.roles && res.data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.data.roles;
              this.permissions = res.data.permissions;
            } else {
              this.roles = ["ROLE_DEFAULT"];
            }
            this.id = user.userId;
            this.name = user.userName;
            this.avatar = avatar;
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取所有待办
    getToDoNoticeList() {
      return new Promise((resolve, reject) => {
        getUnreadNum()
          .then(res => {
            this.backLogNum = res.data;
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            removeToken();
            resolve();
          })
          .catch(error => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            removeToken();
            reject(error);
          });
      });
    }
  }
});

export default useUserStore;
