const useModuleStore = defineStore("screenModule", {
  state: () => ({
    moduleName: "parkOverView",
    domainName: "",
    modelDomain: "",
    modelId: ""
  }),
  actions: {
    setModuleName(val) {
      this.moduleName = val;
    },
    getModuleName() {
      return this.moduleName;
    },
    // 添加本地域名
    setDomine(url) {
      this.domainName = url;
    },
    setmodelDomain(url) {
      this.modelDomain = url;
    },
    setmodelId(id) {
      this.modelId = id;
    }
  }
});
export default useModuleStore;
