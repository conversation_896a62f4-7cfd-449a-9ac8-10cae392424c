// echats 图标自适应

export const fitChatSizeVw = (size, defaultWidth = 1920) => {
  let clintWidth = window.clintWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clintWidth) return size / 2;
  let scale = clintWidth / defaultWidth;
  return Number((size * scale).toFixed(3));
};
export const fitChatSizeVh = (size, defaultHeight = 1080) => {
  let clintHeight = window.clintHeight || document.documentElement.clientHeight || document.body.clientHeight;
  if (!clintHeight) return size / 2;
  let scale = clintHeight / defaultHeight;
  return Number((size * scale).toFixed(3));
};
