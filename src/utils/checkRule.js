import { stringacg } from "./index.js";
export default {
  install(app, options) {
    /**
     *  @param {*}
     *  @returns {Boolean} { true, false }
     *  @description 去除空格后验证是否为空
     * */
    app.config.globalProperties.$isNotNull = value => {
      if (Array.isArray(value)) {
        if (value.length == 0) {
          return false;
        } else {
          return true;
        }
      } else if (typeof value == "string") {
        if (stringacg(value)) {
          return true;
        } else {
          return false;
        }
      } else {
        if (value || value === 0) {
          return true;
        } else {
          return false;
        }
      }
    };

    /**
     *  @param {*} 确认密码 密码
     *  @returns {Boolean} { true, false }
     *  @description 和密码保持一致
     * */
    app.config.globalProperties.$confirmPwordRule = (value, oldvalue) => {
      if (value !== oldvalue) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*} 只能包含大小写字母和数字
     *  @returns {Boolean} { true, false }
     *  @description 只能包含大小写字母 + 数字
     * */
    app.config.globalProperties.$dataBaseRule = value => {
      if (!/^[a-zA-Z0-9]+$/.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*} 只能包含大小写字母,数字,'-','_'
     *  @returns {Boolean} { true, false }
     *  @description 只能包含大小写字母 + 数字 + '-' + '_'
     * */
    app.config.globalProperties.$subCodeRule = value => {
      if (!/^[a-zA-Z0-9\-_]+$/.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*} 邮箱
     *  @returns {Boolean} { true, false }
     *  @description 邮箱规则
     * */
    app.config.globalProperties.$emailRule = value => {
      if (!/^([A-Za-z0-9_\-.\u4e00-\u9fa5])+@([A-Za-z0-9_\-.])+\.([A-Za-z]{2,8})$/g.test(value)) {
        return false;
      } else {
        return true;
      }
    };
    /**
     *  @param {*} 3位纯数字
     *  @returns {Boolean} { true, false }
     *  @description 3位纯数字
     * */
    app.config.globalProperties.$pureNumRule = value => {
      if (!/^[0-9]{3}$/.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*}  密码  P
     *  @returns {Boolean} { true, false }
     *  @description 不能输入中文
     **/
    app.config.globalProperties.$passwordRule = value => {
      if (/[\u4e00-\u9fa5]+/.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*} 手机号   D
     *  @returns {Boolean} { true, false }
     *  @description 11位纯数字
     * */
    app.config.globalProperties.$phoneNumRule = value => {
      if (!/^[0-9]{11}$/.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     * 身份证号码格式验证
     * @param rule 验证规则（是否满足18位）
     * @param value 需要验证的值
     * @param callback 回调函数
     */
    app.config.globalProperties.$validateIDCard = value => {
      const reg = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
      if (!reg.test(value)) {
        return false;
      } else {
        return true;
      }
    };

    /**
     *  @param {*} 数字或小数
     *  @returns {Boolean} { true, false }
     *  @description 是否为数字或小数
     * */
    app.config.globalProperties.$numOrDecimal = value => {
      if (!/^[+]?(0|([1-9]\d*))(\.\d+)?$/g.test(value)) {
        return false;
      } else {
        return true;
      }
    };
    /**
     *  @param {*} 大小写字母
     *  @returns {Boolean} { true, false }
     *  @description 是否为大小写字母
     * */
    app.config.globalProperties.$lowerOrUpperAlphabet = value => {
      if (!/^[a-zA-Z]+$/.test(value)) {
        return false;
      } else {
        return true;
      }
    };
  }
};
