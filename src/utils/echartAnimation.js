// 饼图自动轮播
export function handleChartLoop(option, myChart, time) {
  if (!myChart) {
    return;
  }
  let currentIndex = -1; // 当前高亮图形在饼图数据中的下标
  let changePieInterval = setInterval(selectPie, time); // 设置自动切换高亮图形的定时器

  // 取消所有高亮并高亮当前图形
  function highlightPie() {
    // 遍历饼图数据，取消所有图形的高亮效果
    for (var idx in option.series[0].data) {
      myChart.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: idx
      });
    }
    // 高亮当前图形
    myChart.dispatchAction({
      type: "highlight",
      seriesIndex: 0,
      dataIndex: currentIndex
    });

    // 显示 tooltip
    myChart.dispatchAction({
      type: "showTip",
      seriesIndex: 0,
      dataIndex: currentIndex
    });
  }

  // 用户鼠标悬浮到某一图形时，停止自动切换并高亮鼠标悬浮的图形
  myChart.on("mouseover", params => {
    clearInterval(changePieInterval);
    currentIndex = params.dataIndex;
    highlightPie();
  });

  // 用户鼠标移出时，重新开始自动切换
  myChart.on("mouseout", params => {
    if (changePieInterval) {
      clearInterval(changePieInterval);
    }
    changePieInterval = setInterval(selectPie, 3000);
  });

  // 高亮效果切换到下一个图形
  function selectPie() {
    var dataLen = option.series[0].data.length;
    currentIndex = (currentIndex + 1) % dataLen;
    highlightPie();
  }
}

//坐标图移动
export function autoScrollHandle(option, myChart, time, end, direction = "X") {
  let startValue = 0;
  let endValue = end;
  let maxValue = direction == "X" ? option.xAxis.data.length : option.yAxis.data.length;
  let scrollTimer;

  const scrollEvent = () => {
    if (endValue === maxValue) {
      startValue = 0;
      endValue = end;
      myChart.setOption({
        dataZoom: {
          startValue, // 从头开始。
          endValue
        }
      });
    } else {
      myChart.setOption({
        dataZoom: {
          startValue: ++startValue, // 从头开始。
          endValue: ++endValue
        }
      });
    }
  };

  if (scrollTimer) {
    clearInterval(scrollTimer);
    scrollTimer = null;
  }
  scrollTimer = setInterval(scrollEvent, time);

  // 用户鼠标悬浮到某一图形时，停止自动切换并高亮鼠标悬浮的图形
  myChart.on("mouseover", params => {
    clearInterval(scrollTimer);
    scrollTimer = null;
  });

  // 用户鼠标移出时，重新开始自动切换
  myChart.on("mouseout", params => {
    scrollTimer = setInterval(scrollEvent, time);
  });
}
