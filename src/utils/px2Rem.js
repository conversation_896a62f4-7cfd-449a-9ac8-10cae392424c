export const toRem = px => {
  const baseaSize = getComputedStyle(window.document.documentElement)["font-size"].split("p")[0];

  if (typeof px != "number" && typeof px != "string") {
    throw new Error("出入值类型错误");
  }
  let result;

  if (typeof px == "number") {
    result = `${px / baseaSize}rem`;
  }
  if (typeof px == "string" && px.includes("px")) {
    result = `${px.split("p")[0] / baseaSize}rem`;
  }
  if (typeof px == "string" && px.includes("rem")) {
    result = px;
  }
  return result;
};
export const toSclePx = px => {
  const baseaSize = getComputedStyle(window.document.documentElement)["font-size"].split("p")[0];
  const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";
  const base = storageSetting.fontSizeDefault || 16;
  if (typeof px != "number") {
    throw new Error("出入值类型错误");
  }

  return (baseaSize * px) / base;
};
