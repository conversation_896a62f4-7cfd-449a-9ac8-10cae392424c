//import WebSocket from "websocket";
const socketMap = new Map();
let global_callback_map = new Map();
// const token = getToken();
const baseUrl = `ws://${location.host}`;

class Socket {
  websock = null;
  global_callback = null;
  is_auto_close = false;
  connectNum = 0;
  heartInterval = null;
  timing = 5000;
  open_scoket = false;
  reconnectTimeOut = null;
  createWebSocket(url, callback) {
    if (this.websock == null || typeof this.websock !== WebSocket) {
      this.initWebSocket(url, callback);
    }
  }
  initWebSocket(url, callback) {
    this.global_callback = callback;

    // 初始化websocket
    this.websock = new WebSocket(baseUrl + url);
    //心跳检测
    this.open_scoket = true;
    this.websock.onmessage = e => {
      this.websocketonmessage(e);
      //this.heartCheck(); //心跳检测重置
    };
    this.websock.onclose = e => {
      this.websocketclose(e);
    };
    this.websock.onopen = () => {
      this.websocketOpen();
    };

    // 连接发生错误的回调方法
    this.websock.onerror = function (e) {
      console.log("WebSocket连接发生错误");
    };
  }
  websocketonmessage(msg) {
    console.log(JSON.parse(msg.data));
    console.log(msg, 11232432);
    // 收到信息为Blob类型时
    let result = null;
    // debugger
    if (msg.data instanceof Blob) {
      const reader = new FileReader();
      reader.readAsText(msg.data, "UTF-8");
      reader.onload = e => {
        if (typeof reader.result === "string") {
          result = JSON.parse(reader.result);
        }

        this.global_callback(result);
      };
    } else {
      result = JSON.parse(msg.data);
      if (result?.type == "ping") {
        return;
      }
      if (typeof result == "object" || (typeof result == "string" && !result.includes("连接成功"))) this.global_callback(result);
    }
  }
  heartStart() {
    let self = this;
    if (this.websock.readyState === this.websock.OPEN) {
      this.heartInterval = setInterval(() => {
        this.websocketsend({ type: "ping" });
      }, this.timing);
    }
  }
  closeSock() {
    //手动进行关闭socket
    if (!this.open_scoket) {
      return;
    }
    this.is_auto_close = false;
    this.websock.close();
  }
  sendSock(agentData) {
    if (this.websock.readyState === this.websock.OPEN) {
      // 若是ws开启状态
      this.websocketsend(agentData);
    } else if (websock.readyState === websock.CONNECTING) {
      // 若是 正在开启状态，则等待1s后重新调用
      setTimeout(function () {
        this.sendSock(agentData);
      }, 1000);
    } else {
      // 若未开启 ，则等待1s后重新调用
      setTimeout(function () {
        this.sendSock(agentData);
      }, 1000);
    }
  }
  // 数据发送
  websocketsend(agentData) {
    const data = JSON.stringify(agentData);
    this.websock.send(data);
  }
  // 关闭
  websocketclose(e) {
    //判断是否为手动关闭，非手动关闭需要进行重连操作
    console.log(555555);
    clearInterval(this.heartInterval);
    clearTimeout(this.reconnectTimeOut);
    this.open_scoket = false;

    if (this.connectNum < 5) {
      this.is_auto_close = false;

      this.reconnect();
      this.connectNum++;
    } else {
      this.connectNum = 0;
      this.websock = null;
      console.log("失败重连超过最大次数");
    }

    console.log("connection closed (" + e.code + ")");
  }
  reconnect() {
    //停止心跳
    //clearInterval(this.heartInterval);
    if (!this.open_scoket && !this.is_auto_close) {
      //进行重连,最大延迟30秒重连
      const delay = Math.min(1000 * Math.pow(2, this.connectNum), 30000);
      this.reconnectTimeOut = setTimeout(() => {
        this.initWebSocket();
      }, delay);
    }
  }
  websocketOpen(e) {
    console.log("连接打开");
    this.heartStart();
  }
}

// function createWebSocket(url, callback) {
//   if (socketMap[url] == null || typeof socketMap[url] !== WebSocket) {
//     initWebSocket(url, callback);
//     //socketMap[url] = websock;
//   }
// }
// function initWebSocket(url, callback) {
//   global_callback_map[url] = callback;

//   // 初始化websocket
//   const websock = new WebSocket(baseUrl + url);

//   websock.onmessage = function (e) {
//     websocketonmessage(e, callback);
//   };
//   websock.onclose = function (e) {
//     websocketclose(e);
//   };
//   websock.onopen = function () {
//     websocketOpen();
//   };
//   // 连接发生错误的回调方法
//   websock.onerror = function (e) {
//     console.log(e);
//     console.log("WebSocket连接发生错误");
//   };
//   // return websock;
// }
// //实际调用的方法
// function sendSock(url, agentData) {
//   const websock = socketMap[url];
//   if (websock.readyState === websock.OPEN) {
//     // 若是ws开启状态
//     websocketsend(agentData, websock);
//   } else if (websock.readyState === websock.CONNECTING) {
//     // 若是 正在开启状态，则等待1s后重新调用
//     setTimeout(function () {
//       sendSock(agentData);
//     }, 1000);
//   } else {
//     // 若未开启 ，则等待1s后重新调用
//     setTimeout(function () {
//       sendSock(agentData);
//     }, 1000);
//   }
// }
// function closeSock(url) {
//   socketMap[url].close();
//   socketMap.delete(url);
//   global_callback_map.delete(url);
// }
// // 数据接收
// function websocketonmessage(msg, callback) {
//   console.log("收到数据：" + msg);

//   console.log(JSON.parse(msg.data));
//   // 收到信息为Blob类型时
//   let result = null;
//   // debugger
//   if (msg.data instanceof Blob) {
//     const reader = new FileReader();
//     reader.readAsText(msg.data, "UTF-8");
//     reader.onload = e => {
//       if (typeof reader.result === "string") {
//         result = JSON.parse(reader.result);
//       }
//       //console.log("websocket收到", result);
//       callback(result);
//     };
//   } else {
//     result = JSON.parse(msg.data);
//     //console.log("websocket收到", result);
//     callback(result);
//   }
// }
// // 数据发送
// function websocketsend(agentData, websock) {
//   console.log("发送数据：" + agentData);
//   websock.send(agentData);
// }

export { Socket };
