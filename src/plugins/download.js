﻿import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { blobValidate } from "@/utils/ruoyi";
import axios from "axios/dist/axios";
import { ElLoading, ElMessage } from "element-plus";
import { saveAs } from "file-saver";

const baseURL = import.meta.env.VITE_APP_BASE_API;
let downloadLoadingInstance;

export default {
  oss(ossId) {
    var url = baseURL + "/system/oss/download/" + ossId;
    downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)" });
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    })
      .then(res => {
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          const blob = new Blob([res.data], { type: "application/octet-stream" });
          this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
        } else {
          this.printErrMsg(res.data);
        }
        downloadLoadingInstance.close();
      })
      .catch(r => {
        console.error(r);
        ElMessage.error("下载文件出现错误，请联系管理员！");
        downloadLoadingInstance.close();
      });
  },
  name(name, isDelete = true) {
    let url = baseURL + "/common/download?fileName=" + encodeURIComponent(name) + "&delete=" + isDelete;
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    }).then(res => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  resource(resource) {
    let url = baseURL + "/common/download/resource?resource=" + encodeURIComponent(resource);
    axios({
      method: "get",
      url: url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    }).then(res => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  zip(url, name) {
    downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)" });
    axios({
      method: "get",
      url: baseURL + url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    })
      .then(res => {
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          const blob = new Blob([res.data], { type: "application/zip" });
          this.saveAs(blob, name);
        } else {
          this.printErrMsg(res.data);
        }
        downloadLoadingInstance.close();
      })
      .catch(r => {
        console.error(r);
        ElMessage.error("下载文件出现错误，请联系管理员！");
        downloadLoadingInstance.close();
      });
  },
  file(url) {
    downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)" });
    axios({
      method: "get",
      url: baseURL + url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    })
      .then(res => {
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          const blob = new Blob([res.data]);
          this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
        } else {
          this.printErrMsg(res.data);
        }
        downloadLoadingInstance.close();
      })
      .catch(r => {
        ElMessage.error("下载文件出现错误，请联系管理员！");
        downloadLoadingInstance.close();
      });
  },
  fileFormData(url, data) {
    downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)" });
    axios({
      method: "post",
      url: baseURL + url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken(), "Content-Type": "application/x-www-form-urlencoded" },
      data: data
    })
      .then(res => {
        const isBlob = blobValidate(res.data);
        if (isBlob) {
          const blob = new Blob([res.data]);
          this.saveAs(blob, decodeURIComponent(res.headers["download-filename"]));
        } else {
          this.printErrMsg(res.data);
        }
        downloadLoadingInstance.close();
      })
      .catch(r => {
        ElMessage.error("下载文件出现错误，请联系管理员！");
        downloadLoadingInstance.close();
      });
  },
  fileAndURL(url) {
    return axios({
      method: "get",
      url: baseURL + url,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() }
    }).then(res => {
      // const isBlob = blobValidate(res.data);
      // if (isBlob) {

      const blob = new Blob([res.data], { type: "application/pdf" });
      const src = URL.createObjectURL(blob);
      const codeSrc = src;
      return Promise.resolve(codeSrc);
      // } else {
      //   this.printErrMsg(res.data);
      // }
    });
    // .catch(r => {

    //   ElMessage.error("下载文件出现错误，请联系管理员！");
    // });
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
    ElMessage.error(errMsg);
  }
};
