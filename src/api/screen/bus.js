import request from "@/utils/request";

/**
 * 查询所有公交车的实时位置
 * @param {*} query
 * @returns
 */
export const queryAllBusCarLocation = query => {
  return request({
    url: "/bus/getAllBusCarLocation",
    method: "get",
    params: query
  });
};
/**
 * 查询今日公交接驳任务
 * @param {*} query
 * @returns
 */
export const queryTodayBusTask = query => {
  return request({
    url: "/bus/todayBusTask",
    method: "get",
    params: query
  });
};
