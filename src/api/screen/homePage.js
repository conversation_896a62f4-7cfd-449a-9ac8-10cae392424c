import request from "@/utils/request";

/**
 * 作业统计
 * @param {*} query
 * @returns
 */
export const queryWork = query => {
  return request({
    url: "/homePage/dispatchCount",
    method: "get",
    params: query
  });
};

/**
 * 今日通行次数
 * @param {*} query
 * @returns
 */
export const transitCount = () => {
  return request({
    url: "/homePage/transitCount",
    method: "get"
  });
};

/**
 * 通行趋势
 * @param {*} query
 * @returns
 */
export const transitTrends = (query) => {
  return request({
    url: "homePage/transitTrends",
    method: "get",
    params: query
  });
};