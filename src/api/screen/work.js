import request from "@/utils/request";
// 新增作业计划
export function submitAddTask(data) {
  return request({
    url: "/work/addTask",
    method: "post",
    data
  });
}
//修改作业计划
export function submitUpdateTask(data) {
  return request({
    url: "/work/updateTask",
    method: "get",
    params: data
  });
}

/**
 * 作业班组查询
 * @param {*} query
 * @returns
 */
export const queryTeamListRequest = groupName => {
  if (!groupName) {
    groupName = "";
  }
  return request({
    url: `/dispatch/group/list?groupName=${groupName}`
  });
};
//班组info
export const queryTeamInfoRequest = groupId => {
  return request({
    url: `/dispatch/group/getGroupById/${groupId}`
  });
};
/**
 * 作业详情查询
 * @param {taskId} taskId 作业详情的id
 * @returns
 */
export const getTaskDetails = taskId => {
  return request({
    url: `/work/getTaskDetails/${taskId}`
  });
};
/**
 * 修改作业弹框查询详情查询
 * @param {taskId} taskId 作业详情的id
 * @returns
 */
export const getTaskInfo = taskId => {
  return request({
    url: `/work/getTaskInfo/${taskId}`
  });
};
