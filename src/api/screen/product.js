import request from "@/utils/request";

/**
 * 作业类型查询
 * @param {*} query
 * @returns
 */
export const queryWorkType = query => {
  return request({
    url: "/work/getKindList",
    method: "get",
    params: query
  });
};
/**
 * 作业告警查询
 * @param {*} query
 * @returns
 */
export const queryTransitAlarm = query => {
  return request({
    url: "/transitAlarm/getList",
    method: "get",
    params: query
  });
};
/**
 * 今日任务列表
 * @param {*} query
 * @returns
 */
export const queryTaskList = query => {
  return request({
    url: "/work/getTodayTaskList",
    method: "get",
    params: query
  });
};
/**
 * 获取所有车辆实时位置
 * @param {*} query
 * @returns
 */
export const queryAllCarLocation = query => {
  return request({
    url: "/work/getAllWorkCarLocation",
    method: "get"
  });
};
/**
 * 获取当前车辆实时位置
 * @param {carNo} carNo
 * @returns
 */
export const queryCarLocation = query => {
  return request({
    url: "/work/getCarLocation",
    method: "get",
    params: query
  });
};
/**
 * 获取当前车辆轨迹
 * @param {taskId} taskId
 * @returns
 */
export const queryCarTrak = taskId => {
  return request({
    url: `/work/getCarTrackOfTask/${taskId}`,
    method: "get"
  });
};

// 根据条件查询作业路线
export function getRouteList(query) {
  return request({
    url: "/dispatch/route/getRoutes",
    method: "get",
    params: query
  });
}
// 根据id查询路线
export function getRouteById(routeId) {
  return request({
    url: `/dispatch/route/${routeId}`,
    method: "get"
  });
}

/**
 * 查询告警列表
 * @param {*} query
 * @returns
 */
export const queryTodayAlarmList = query => {
  return request({
    url: "/work/getTodayAlarmList",
    method: "get",
    params: query
  });
};
/**
 * 告警详情查询
 * @param {alarmId} alarmId 作业详情的id
 * @returns
 */
export const getAlarmDetails = alarmId => {
  return request({
    url: `/work/getAlarmDetails/${alarmId}`
  });
};

/**
 * 告警前后行车记录仪视频
 * @param {alarmId} alarmId 作业详情的id
 * @returns
 */
export const getAlarmVideo = alarmId => {
  return request({
    url: `/work/getAlarmPlayback`,
    method: "get",
    params: { alarmId }
  });
};
/**
 * 汽车前后行车记录仪视频
 * @param {carId} carId 作业详情的id
 * @returns
 */
export const getCarVideo = carId => {
  return request({
    url: `/work/getRealTimeMonitor`,
    method: "get",
    params: { carId }
  });
};
