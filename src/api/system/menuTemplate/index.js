import request from "@/utils/request";

// 查询菜单模板列表
export function getMenuTemList(query) {
  return request({
    url: "/project/menuTemplate/list",
    method: "get",
    params: query
  });
}

// 新增菜单模板
export function addMenuTem(data) {
  return request({
    url: "/project/menuTemplate",
    method: "post",
    data: data
  });
}

// 修改菜单模板
export function updateMenuTem(data) {
  return request({
    url: "/project/menuTemplate",
    method: "put",
    data: data
  });
}

// 删除菜单模板
export function delMenuTem(menuId) {
  return request({
    url: "/project/menuTemplate/" + menuId,
    method: "delete"
  });
}

// 查询所有菜单下拉树结构
export function treeselectAll() {
  return request({
    url: "/system/menu/treeselectAll",
    method: "get"
  });
}

// 查询所有模板菜单
export function templateTreeselectAll(query) {
  return request({
    url: "/system/menu/templateTreeselectAll",
    method: "get",
    params: query
  });
}
// 更新用户模板
export function updateUserMenuTem(data) {
  return request({
    url: "/system/user/updateTemplateId",
    method: "put",
    data: data
  });
}
