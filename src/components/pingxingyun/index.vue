<template>
  <div class="source" id="source" ref="appRef">
    <!-- 引入腾讯地图作为背景 -->
    <YunChart :curAppId="currentId" ref="yunChart" class="background-chart" @marker-click="markerClick"></YunC<PERSON>>
    <div class="testBu flex aligns_center spaceb">
      <div class="ml10 mr10" style="width: 10vw">
        <div class="text-center" style="transform: scale(0.8)">
          {{ timeNow.time }}
        </div>
        <div class="text-center">
          {{ timeNow.weekDay }}
        </div>
      </div>
      <div class="titleGrad aligns_center" style="flex-grow: 1">
        <div v-for="item in Object.keys(views).filter(item => ['ProductionTransport', 'BusDispatch', 'PassSafe'].includes(item))" :key="item">
          <div class="pointer flex spacec" @click="viewCurComponent(item)">
            <div :class="currentKey == views[item].key ? 'highlight' : ''" class="item_tab">{{ views[item].name }}</div>
          </div>
        </div>

        <div class="text-center pointer title" @click="viewCurComponent('HomePage')">今世缘酒业智慧通行</div>

        <div v-for="item in Object.keys(views).filter(item => ['OnSiteLogistics', 'OutSiteLogistics'].includes(item))" :key="item">
          <div class="pointer flex spacec" @click="viewCurComponent(item)">
            <div :class="currentKey == views[item].key ? 'highlight' : ''" class="item_tab">
              {{ views[item].name }}
            </div>
          </div>
        </div>
      </div>

      <div class="flex aligns_center ml10 mr10" style="width: 10vw">
        <div class="text-center" style="transform: scale(0.8)">
          <span @click="changeMapType" class="pointer">{{ mapType == "vector" ? "卫星图" : "地图" }}</span> &nbsp;
          <span> /</span>
        </div>

        <div class="ml10 mr10 flex" style="transform: scale(0.8)">
          <!-- <div class="weather" style="align-self: center">
            <img :src="weatherCloudy" alt="" style="width: 2vw" />
          </div> -->
          <div style="align-self: center" class="ml5">多云</div>
        </div>
      </div>
    </div>

    <component :is="currentCompont" @changeComp="viewCurComponent"></component>
    <!-- 作业详情 -->
    <WorkInfo v-show="isShowWork" ref="workInfoRef" :carTypes="carTypes"></WorkInfo>
    <!-- 告警详情 -->
    <AlarmInfo v-show="isShowAlarm" ref="alarmInfoRef"></AlarmInfo>
    <!-- 作业弹框 -->
    <AddWork ref="addWorkRef" :groupList="groupList"></AddWork>
    <!-- 汽车前后视频 -->
    <CarVideo ref="carVideoRef" v-show="isShowCarVideo"></CarVideo>

    <TextBox
      v-if="currentKey === 'false'"
      @setAllPoints="setAllPoints"
      @resetMarkers="resetMarkers"
      @setRoute="setRoute"
      @clearRoute="clearRoute"
    ></TextBox>
  </div>
</template>
<script setup>
import { queryTeamListRequest } from "@/api/screen/work.js";
import { getDicts } from "@/api/system/dict/data.js";
import useDraw from "@/utils/useDraw";
import { onBeforeUnmount, reactive, shallowRef, watch } from "vue";
import HomePage from "./components/homePage/index.vue";
import TextBox from "./components/passSafe/textBox.vue";
import YunChart from "./components/txMap.vue";
import { event } from "./event.js";
import { formatTime, getDay } from "./fullScreen.js";

const ProductionTransport = defineAsyncComponent(() => import("./components/productionTransport/index.vue")); //生产运输
const BusDispatch = defineAsyncComponent(() => import("./components/busDispatch/index.vue")); //公交调度
const PassSafe = defineAsyncComponent(() => import("./components/passSafe/index.vue")); //通行安全
const WorkInfo = defineAsyncComponent(() => import("./components/workInfo/index.vue"));
const AlarmInfo = defineAsyncComponent(() => import("./components/alarmInfo/index.vue"));
const AddWork = defineAsyncComponent(() => import("./components/addWork/index.vue"));
const CarVideo = defineAsyncComponent(() => import("./components/carVideo/index.vue"));
const yunChart = ref(null);
const currentKey = ref("");
const workInfoRef = ref(null);
const alarmInfoRef = ref(false);
const isShowWork = ref(false);
const isShowAlarm = ref(false);
const isShowCarVideo = ref(false);
const currentId = "1267514226449055744";
const currentCompont = shallowRef(null);
const { appRef } = useDraw();
const addWorkRef = ref(null);
const carVideoRef = ref(null);
const kindList = shallowRef([]);
const mapType = ref("vector"); //'satellite'为卫星图，'vector'为矢量图
const maps = {
  HomePage,
  ProductionTransport,
  BusDispatch,
  PassSafe
};
const views = reactive({
  HomePage: { name: "首页", key: "HomePage", args: 1 },
  ProductionTransport: { name: "生产运输", key: "ProductionTransport", args: 2 },
  BusDispatch: { name: "公交调度", key: "BusDispatch", args: 3 }
  // PassSafe: { name: "通行安全", key: "PassSafe", args: 4 },
  // OnSiteLogistics: { name: "场内物流车辆", key: "OnSiteLogistics", args: 5 },
  // OutSiteLogistics: { name: "场外物流车俩", key: "OutSiteLogistics", args: 6 }
});
const timeNow = reactive({ time: formatTime(null, "YY-MM-DD HH:mm:ss"), weekDay: getDay() });
const interval = setInterval(() => {
  timeNow.time = formatTime(null, "YY-MM-DD HH:mm:ss");
  timeNow.weekDay = getDay();
}, 1000);
watch(
  () => isShowWork.value,
  val => {
    if (!val) workInfoRef.value?.clearOPerate();
  }
);
watch(
  () => isShowAlarm.value,
  val => {
    if (!val) alarmInfoRef.value?.destroy();
  }
);
watch(
  () => isShowCarVideo.value,
  val => {
    if (!val) carVideoRef.value?.destroy();
  }
);
// 查找班组
const groupList = ref([]);
const getGroup = async () => {
  const res = await queryTeamListRequest();
  if (res.code == 200) {
    groupList.value = res.data;
  }
};
// 获取车辆类型
let carTypes = ref([]);
const getCarTypes = async () => {
  const res = await getDicts("dispatch_car_type");
  carTypes.value = res?.data;
};
getCarTypes();
const changeMapType = () => {
  mapType.value = mapType.value == "vector" ? "satellite" : "vector";
  yunChart.value?.changeMapType(mapType.value);
};
getGroup();

onMounted(async () => {
  //初始化

  event.on("carVideoInfo", info => {
    const { id } = info;
    isShowCarVideo.value = !isShowCarVideo.value;
    isShowCarVideo.value && carVideoRef.value?.queryNewWork(id);
  });
  event.on("alarmInfo", info => {
    isShowAlarm.value = !isShowAlarm.value;
    isShowAlarm.value && alarmInfoRef.value?.queryNewWork(info);
  });
  event.on("workInfo", info => {
    clearRoute();
    if (info.taskId) {
      //显示作业信息
      resetMarkers();
      isShowWork.value = true;
      if (isShowAlarm.value) {
        //如果告警信息也显示了，需要先隐藏告警信息并且将单击选中的告警key去掉
        isShowAlarm.value = false;
        event.emit("clearCurAlarmId");
      }
      workInfoRef.value?.queryNewWork(info);
    } else {
      //隐藏作业信息,告警信息，以及可能出现的车辆视频
      isShowWork.value = false;
      isShowAlarm.value = false;
      isShowCarVideo.value = false;
    }
  });
  //展示当前任务车辆实时位置
  event.on("showCurCar", points => {
    updatePoints(points, "carDown");
  });
  //展示当前路线中车辆行驶轨迹
  event.on("showCurTrak", info => {
    setRoute(info.path, "trak");
    // movAlong(info);
  });
  //新增作业
  event.on("addNewWork", info => {
    addWorkRef.value?.openDialog(info);
  });
  //修改作业
  event.on("editWork", info => {
    addWorkRef.value?.openDialog(info);
  });
  event.on("showAllCarMarkers", points => {
    resetMarkers();
    setAllPoints(points);
  });
  //展示作业详情中的线路和起始点和告警点位
  event.on("showRoute", val => {
    const { points, routePlan } = val;
    setAllPoints(points);
    setRoute(routePlan);
  });
  currentKey.value = "HomePage";
  currentCompont.value = HomePage;
});

//切换不同的模型
const viewCurComponent = val => {
  if (!maps[val]) return;
  if (currentKey.value == val) return;
  currentKey.value = val;
  currentCompont.value = maps[val];
  isShowWork.value = false;
  isShowAlarm.value = false;
  isShowCarVideo.value = false;
  resetMarkers();
  clearRoute();
};
const markerClick = info => {
  const { id, geometry } = info;
  if (geometry.styleId == "camera") {
    event.emit("carVideoInfo", { id: id.replace("camera", "") });
  }
  if (geometry.styleId == "alarm") {
    event.emit("alarmInfo", { id: id });
  }
};

const setAllPoints = list => {
  yunChart.value.setMarkers(list);
};
const updatePoints = list => {
  yunChart.value.updateMarkers(list);
};
const resetMarkers = () => {
  yunChart.value.resetMarkers();
};
const setRoute = (...val) => {
  yunChart.value.setRoute(val[0], val[1]);
};
const movAlong = val => {
  yunChart.value.movAlong(val);
};
const clearRoute = () => {
  yunChart.value.removeRoute();
};

onBeforeUnmount(() => {
  clearInterval(interval);
});
</script>
<style>
.el-notification .el-notification--info {
  color: #f2f2f2;
}
.el-notification .el-notification__content {
  color: #f2f2f2;
}
.el-notification {
  border: 0px;
  background-color: rgba(39, 84, 159, 0.6);
}
.el-dropdown-menu__item {
  padding: 0.2vh 0.2vw;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
$loaderHeight: 180px;

$hederButtoncolor: #a5d8fc;
/* 添加背景图表样式 */
.background-chart {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 确保背景在最底层 */
}

.loader {
  width: $loaderHeight;
  height: $loaderHeight;
  line-height: $loaderHeight;
  box-sizing: border-box;
  text-align: center;
  z-index: 0;
  text-transform: uppercase;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -90px;
  margin-top: -90px;
  font-size: 14px;
}
.loderSet {
  width: 100vw;
  height: 100vh;
  z-index: 5;
  background-color: #fff;
}
.loader:before,
.loader:after {
  opacity: 0;
  box-sizing: border-box;
  content: "\0020";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 100px;
  border: 5px solid #fff;
  box-shadow:
    0 0 50px #fff,
    inset 0 0 50px #fff;
}

.loader:after {
  z-index: 1;
  animation: gogoloader 2s infinite 1s;
}

.loader:before {
  z-index: 2;
  animation: gogoloader 2s infinite;
}

@keyframes gogoloader {
  0% {
    -webkit-transform: scale(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1);
    opacity: 0;
  }
}
.source {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  // background-color: #112142;
  z-index: 1;
  font-size: vh(24);
}

.testBu {
  position: absolute;

  padding: 0 0.5vw;
  height: vh(80);
  top: 0;
  left: 0;
  right: 0;
  // height: vh(76);
  z-index: 3;
  background-color: var(--color-primary);
  color: #fff;
  font-size: $vw16;
  .title {
    font-weight: 700;
    font-style: normal;
    font-size: vw(32);

    // margin: auto;
  }
}
.titleGrad {
  display: grid;
  grid-template-columns: 2fr 2fr 4fr 2fr 2fr;
}
.highlight {
  border-radius: vw(10) vw(10) 0 0;
  background-color: rgba(255, 255, 255, 0.3);
}
.item_tab {
  width: fit-content;
  padding: vw(5) vw(10);
}
</style>
