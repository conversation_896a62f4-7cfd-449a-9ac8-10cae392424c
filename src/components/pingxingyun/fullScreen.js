const enterFullScreen = dom => {
  //w3c
  if (dom.requestFullscreen) {
    dom.requestFullscreen();
  }
  //谷歌
  else if (dom.webkitRequestFullScreen) {
    dom.webkitRequestFullScreen();
  }
  //火狐
  else if (dom.mozRequestFullScreen) {
    dom.mozRequestFullScreen();
  }
  //ie
  else if (dom.msRequestFullscreen) {
    dom.msRequestFullscreen();
  }
};
//退出
const exitFullScreen = () => {
  //w3c
  if (document.exitFullscreen) {
    document.exitFullscreen();
  }
  //谷歌
  else if (document.webkitCancelFullScreen) {
    document.webkitCancelFullScreen();
  }
  //火狐
  else if (document.mozCanceltFullScreen) {
    document.mozCanceltFullScreen();
  }
  //ie
  else if (document.msExitFullScreen) {
    document.msExitFullScreen();
  }
};
//判断是否已经全屏
const isFullScreen = () => {
  return document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen;
};
export const toggle = dom => {
  !isFullScreen() ? enterFullScreen(dom) : exitFullScreen();
};
//时间格式
export const formatTime = (time, format) => {
  const date = time ? new Date(time) : new Date();
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hour = date.getHours().toString().padStart(2, "0");
  const minute = date.getMinutes().toString().padStart(2, "0");
  const second = date.getSeconds().toString().padStart(2, "0");

  let result = "";

  switch (format) {
    case "yyyy-MM-dd":
      result = `${year}-${month}-${day}`;
      break;
    case "yyyy/MM/dd":
      result = `${year}/${month}/${day}`;
      break;
    case "yyyy年MM月dd日":
      result = `${year}年${month}月${day}日`;
      break;
    case "HH:mm:ss":
      result = `${hour}:${minute}:${second}`;
      break;
    default:
      result = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }

  return result;
};
export const getDay = time => {
  const weekdays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
  const date = new Date();

  return weekdays[date.getDay()];
};
import { ref } from "vue";
let currentIntIndex = 0;
export const useScroll = () => {
  const visablePark = ref(null);
  const listRef = ref(null);
  const isinitAnimation = ref(true);
  const tableData = ref([]);
  let lastTime;
  const startScroll = val => {
    if (visablePark.value) {
      /*有动画 先移除动画*/
      //clearInterval(visablePark.value);
      window.cancelAnimationFrame(visablePark.value);
    }
    //一次展示2个，3vh
    if (tableData.value.length <= 2 && isinitAnimation.value) {
      listRef.value.style.transform = "translateY(0)";
    } else {
      //在末尾添加两个来用于无感移动
      const copyData = tableData.value.slice(0, 2);
      isinitAnimation.value && tableData.value.push(...copyData);

      //visablePark.value = setInterval(onScroll, 2000);
      visablePark.value = window.requestAnimationFrame(onScroll(val));
    }
  };

  const onScroll = (val = 3) => {
    const currentTime = performance.now();
    currentIntIndex++;
    if (currentIntIndex >= tableData.value.length - 2) {
      currentIntIndex = 0;
    }
    if ((lastTime == undefined || currentTime - lastTime >= 2000) && listRef.value) {
      listRef.value.style.transform = `translateY(${-(val * currentIntIndex)}vh)`;
      lastTime = currentTime;
    }
    visablePark.value = window.requestAnimationFrame(onScroll(val));
  };
  const stop = () => {
    if (visablePark.value) {
      isinitAnimation.value = false;
      clearInterval(visablePark.value);
    }
  };
  return {
    startScroll,
    stop,
    onScroll,
    isinitAnimation,
    listRef,
    visablePark,
    tableData
  };
};
