// export const event = {
//   map: {},
//   off() {
//     Object.keys(this.map).forEach(name => {
//       window.removeEventListener(name);
//     });
//   },
//   emit(name, data) {
//     if (this.map[name]) {
//       this.map[name].detail = data;
//       window.dispatchEvent(this.map[name]);
//     }
//   },
//   on(name, cb) {
//     let event = new Event(name);
//     this.map[name] = event;

//     window.addEventListener(name, e => {
//       cb(this.map[name].detail);
//     });
//   }
// };
export const event = {
  map: {},
  off(name, cb) {
    if (this.map[name]) {
      delete this.map[name];
      //this.map[event] = this.map[name].filter(l => l !== cb);
    }
  },
  emit(name, ...data) {
    if (this.map[name]) {
      console.log(this.map[name]);
      const events = this.map[name];
      events(...data);
      // this.map[name].forEach(listen => {
      //   listen(...data);
      // });
    }
  },
  on(name, cb) {
    // if (!this.map[name]) {
    //   this.map[name] = [];
    this.map[name] = cb;
    //}

    // window.addEventListener(name, e => {
    //   cb(this.map[name].detail);
    // });
  }
};
export const useDebounce = (fn, delay = 3000) => {
  let timer;
  return function (...args) {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
      timer = null;
      fn && fn.apply(this, args);
    }, delay);
  };
};
