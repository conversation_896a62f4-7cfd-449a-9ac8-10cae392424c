export const getAllPoints = (points, styleName = "homeworkCar") => {
  let newList = points.map(item => {
    let coordinate = item.coordinate;
    if (styleName === "currentPostion") {
      return {
        id: item.pointId,
        styleId: styleName,
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `当前位置：${item.pointName}`, // 标注点名称
          driver: `驾驶员：${item.driver}`, // 打卡状态
          clockTime: `车辆类型：${item.carType}`, // 标注点时间
          currentWork: `当前任务：原料运输`, // 标注点时间
          dest: `目的地：二号厂区`, // 标注点时间
          pointId: item.pointId,
          showWindow: false // 是否显示窗口
        }
      };
    } else {
      return {
        id: item.pointId,
        styleId: styleName,
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          pointId: item.pointId
        }
      };
    }
  });
  return newList;
};
export const getRoutePoints = routePoints => {
  let newList = routePoints.map((item, index) => {
    let coordinate = item.coordinate;
    if (index === 0) {
      return {
        id: item.pointId,
        styleId: "start",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName || ""}`, // 标注点名称
          positionStatus: `点位状：${item.status || "未打卡"}`, // 打卡状态
          clockTime: `打卡时间：2024-07-10 10:00:00`, // 标注点时间
          showWindow: false // 是否显示窗口
        }
      };
    } else if (index === routePoints.length - 1) {
      return {
        id: item.pointId,
        styleId: "end",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName || ""}`, // 标注点名称
          positionStatus: `点位状：已打卡`, // 打卡状态
          clockTime: `打卡时间：${item.time || ""}`, // 标注点时间
          showWindow: false // 是否显示窗口
        }
      };
    } else {
      return {
        id: item.pointId,
        styleId: "routeItem",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName || ""}`, // 标注点名称
          positionStatus: `点位状：已打卡`, // 打卡状态
          clockTime: `打卡时间：${item.time || ""}`, // 标注点时间
          showWindow: false // 是否显示窗口
        }
      };
    }
  });
  return newList;
};
