export const userGrow = (time = 2) => {
  const numberGrow = (ele, newValue, oldValue) => {
    //【这里调速度 1 ，步进值， 通俗地讲，就是每次跳的时候，增加的一个增量】
    const value = newValue - oldValue;
    let step = parseInt((value * 100) / (time * 1000));
    // 设置当前值(这个值是计时器持续运行时，每次页面上显示的跳动值，不是最终的那个具体值)
    let current = oldValue;
    // 设置开始值
    let start = oldValue;
    // 设置定时器，用来反复横跳的，哈哈哈

    let t = setInterval(() => {
      // 每次增加一点步进值
      start += step;
      // 到点了，不用 继续横跳了
      if ((step > 0 && start > newValue) || (step < 0 && start < newValue)) {
        clearInterval(t);
        // 把穿过的值赋给start，结束
        start = newValue;
        // 清掉计时器
        t = null;
      }
      if (start == 0 || step == 0) {
        start = newValue;
        clearInterval(t);
      }

      current = start;

      // 正则
      ele.innerHTML = current.toString().replace(/(\d)(?=(?:\d{3}[+]?)+$)/g, "$1,");
    }, time * 100); // 【这里调速度 2， 通俗地讲，这里是页面上，肉眼能看到的跳动频率】
  };

  return {
    numberGrow
  };
};
export const userShowModel = (time = 2) => {
  const isShowModel = ref(true);
  const isShowModelRight = ref(true);
  const changeModelRight = val => {
    isShowModelRight.value = val;
  };
  return {
    isShowModel,
    isShowModelRight,
    changeModelRight
  };
};
