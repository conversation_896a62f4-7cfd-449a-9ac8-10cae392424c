<template>
  <div class="poi_abs z-in2 workTop flex">
    <el-card style="width: 14vw">
      <div class="everyTitle">前行车记录仪告警详情</div>
      <div style="width: 15vw">
        <VideoPlayer ref="forwordViedo"></VideoPlayer>
      </div>
    </el-card>
    <el-card style="margin-left: 1vw; width: 14vw">
      <div class="everyTitle">后行车记录仪告警详情</div>
      <div>
        <VideoPlayer ref="behindViedo"></VideoPlayer>
      </div>
    </el-card>
  </div>
</template>
<script setup>
import { getCarVideo } from "@/api/screen/product";
import VideoPlayer from "@/components/pingxingyun/components/videoPlayer";

const forwordViedo = ref(null);
const behindViedo = ref(null);
const queryNewWork = async val => {
  const res = await getCarVideo(val);
  if (res.code == 200) {
    forwordViedo.value?.playVideo(res.data[0].recorderCode);
    if (res.data.length > 1) behindViedo.value?.playVideo(res.data[1].recorderCode);
  }
};
const destroy = () => {
  forwordViedo.value?.destroyFlv();
  behindViedo.value?.destroyFlv();
};
defineExpose({ queryNewWork, destroy });
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.workTop {
  top: vh(100);
  left: calc(15.5vw + vw(344));
  font-size: $vw14;
}
::v-deep .el-card {
  // background-color: rgba(141 92 139 / 50%);
}
</style>
