<template>
  <div class="text-container">
    <el-button type="primary" @click="setAllPoints">地图打点</el-button>
    <el-button type="primary" @click="resetMarkers">清空地图上的打点</el-button>
    <el-button type="primary" @click="setRoute">地图上画路线</el-button>
    <el-button type="primary" @click="clearRoute">清空地图上的路线</el-button>
  </div>
</template>

<script setup>
const emits = defineEmits(["setAllPoints", "resetMarkers", "setRoute", "clearRoute"]);
let allPoints = [
  {
    pointId: "1909904898880434178",
    routeId: "1909904898872045569",
    serialNum: 1,
    pointName: "南厂西门",
    pointCode: "ximenzhan",
    pointType: "0",
    coordinate: "119.1767006840305,34.00190018591536"
  },
  {
    pointId: "1909904898884628482",
    routeId: "1909904898872045569",
    serialNum: 2,
    pointName: "酿酒11车间",
    pointCode: "N-28",
    pointType: "0",
    coordinate: "119.17800857465295,33.9997098077103"
  },
  {
    pointId: "1909904898884628483",
    routeId: "1909904898872045569",
    serialNum: 3,
    pointName: "酿酒12车间",
    pointCode: "N-29",
    pointType: "0",
    coordinate: "119.17963760235546,33.999396973849535"
  },
  {
    pointId: "1909904898884628484",
    routeId: "1909904898872045569",
    serialNum: 4,
    pointName: "酿酒13车间",
    pointCode: "N-30",
    pointType: "0",
    coordinate: "119.18245491040284,33.998879678719874"
  },
  {
    pointId: "1909904898884628485",
    routeId: "1909904898872045569",
    serialNum: 5,
    pointName: "二道门",
    pointCode: "2daomenzhan",
    pointType: "0",
    coordinate: "119.19207597488162,34.00657016211429"
  },
  {
    pointId: "1909904898888822785",
    routeId: "1909904898872045569",
    serialNum: 6,
    pointName: "三号门",
    pointCode: "3haomenzhan",
    pointType: "0",
    coordinate: "119.19351850916507,34.00869455978268"
  }
];
const getAllPoints = (points, styleName = "homeworkCar") => {
  let newList = points.map(item => {
    let coordinate = item.coordinate.split(",");
    if (styleName === "currentPostion") {
      return {
        id: item.pointId,
        styleId: styleName,
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `当前位置：${item.pointName}`, // 标注点名称
          driver: `驾驶员：张三`, // 打卡状态
          clockTime: `车辆类型：大型货车`, // 标注点时间
          currentWork: `当前任务：原料运输`, // 标注点时间
          dest: `目的地：二号厂区`, // 标注点时间
          showWindow: true // 是否显示窗口
        }
      };
    } else {
      return {
        id: item.pointId,
        styleId: styleName,
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]) // 标注点位置
      };
    }
  });
  return newList;
};
let routePoints = [
  {
    pointId: "1873934808398884866",
    routeId: "1854479945849577474",
    serialNum: 1,
    pointName: "南厂区西门站",
    pointCode: "ximenzhan",
    pointType: "0",
    coordinate: "119.17725286027257,34.00183919376709"
  },
  {
    pointId: "1873934808407273473",
    routeId: "1854479945849577474",
    serialNum: 2,
    pointName: "南厂食堂站",
    pointCode: "shitangzhan",
    pointType: "0",
    coordinate: "119.17756423807577,34.0026671005234"
  },
  {
    pointId: "1909872318326177794",
    routeId: "1854479945849577474",
    serialNum: 3,
    pointName: "仓储物流站",
    pointCode: "ccwlz",
    pointType: "0",
    coordinate: "119.18070930452427,34.00268533674316"
  },
  {
    pointId: "1909872318330372098",
    routeId: "1854479945849577474",
    serialNum: 4,
    pointName: "包装站",
    pointCode: "baozhuangzhan",
    pointType: "0",
    coordinate: "119.18370008395948,34.0021587834412"
  },
  {
    pointId: "1909872495120285698",
    routeId: "1854479945849577474",
    serialNum: 5,
    pointName: "白金酒库站",
    pointCode: "bjjkz",
    pointType: "0",
    coordinate: "119.19033216865785,34.003097635638255"
  },
  {
    pointId: "1909872495124480001",
    routeId: "1854479945849577474",
    serialNum: 6,
    pointName: "二道门站",
    pointCode: "erdaomen",
    pointType: "0",
    coordinate: "119.19205537338075,34.0065804432887"
  },
  {
    pointId: "1909872495128674306",
    routeId: "1854479945849577474",
    serialNum: 7,
    pointName: "三号门站",
    pointCode: "sanhaomenzhan",
    pointType: "0",
    coordinate: "119.19347870254273,34.00872628866673"
  },
  {
    pointId: "1909872495132868609",
    routeId: "1854479945849577474",
    serialNum: 8,
    pointName: "包装二车间站",
    pointCode: "bzecjz",
    pointType: "0",
    coordinate: "119.19379276699692,34.012441655301515"
  }
];
let routePlan = [
  {
    lat: "34.00183919376709",
    lng: "119.17725286027257"
  },
  {
    lat: "34.00183201593663",
    lng: "119.17743597730237"
  },
  {
    lat: "34.00314029070371",
    lng: "119.17771701076504"
  },
  {
    lat: "34.00319405498205",
    lng: "119.1778142915615"
  },
  {
    lat: "34.00319405498205",
    lng: "119.1779548081895"
  },
  {
    lat: "34.00289835099266",
    lng: "119.17926269445718"
  },
  {
    lat: "34.00264497694563",
    lng: "119.1807029443969"
  },
  {
    lat: "34.002365315029245",
    lng: "119.18236409978158"
  },
  {
    lat: "34.00208001196352",
    lng: "119.18397302805602"
  },
  {
    lat: "34.00199752828261",
    lng: "119.18434692018548"
  },
  {
    lat: "34.00303911453465",
    lng: "119.1847420199341"
  },
  {
    lat: "34.004015180653184",
    lng: "119.18491586384152"
  },
  {
    lat: "34.003751988181314",
    lng: "119.18685139046443"
  },
  {
    lat: "34.00335217074812",
    lng: "119.18933702409754"
  },
  {
    lat: "34.00306219209392",
    lng: "119.19108067732407"
  },
  {
    lat: "34.0049116154497",
    lng: "119.19157216645044"
  },
  {
    lat: "34.0065804432887",
    lng: "119.19205537338075"
  },
  {
    lat: "34.006594458973446",
    lng: "119.19207095000581"
  },
  {
    lat: "34.0075564915463",
    lng: "119.19237255396433"
  },
  {
    lat: "34.00872628866673",
    lng: "119.19347870254273"
  },
  {
    lat: "34.00876745569782",
    lng: "119.19346457711845"
  },
  {
    lat: "34.01208621776482",
    lng: "119.19680930134086"
  },
  {
    lat: "34.01290627049769",
    lng: "119.1940105525357"
  },
  {
    lat: "34.012441655301515",
    lng: "119.19379276699692"
  }
];
let currentPostion = [
  {
    pointId: "1",
    pointName: "三号门",
    coordinate: "119.18731056073943,34.0036906518539"
  }
];
let cameraPostion = [
  {
    pointId: "9",
    pointName: "三号门",
    coordinate: "119.18731056073943,34.0036906518539"
  }
];
let redWarning = [
  {
    pointId: "2",
    pointName: "三号门",
    coordinate: "119.19151625268773,34.00468679558909"
  }
];
let greenWarning = [
  {
    pointId: "3",
    pointName: "三号门",
    coordinate: "119.18585144316899,34.00386853501657"
  }
];
const getRoutePoints = routePoints => {
  let newList = routePoints.map((item, index) => {
    let coordinate = item.coordinate.split(",");
    if (index === 0) {
      return {
        id: item.pointId,
        styleId: "start",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName}`, // 标注点名称
          positionStatus: `点位状：已打卡`, // 打卡状态
          clockTime: `打卡时间：2024-07-10 10:00:00`, // 标注点时间
          showWindow: true // 是否显示窗口
        }
      };
    } else if (index === routePoints.length - 1) {
      return {
        id: item.pointId,
        styleId: "end",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName}`, // 标注点名称
          positionStatus: `点位状：已打卡`, // 打卡状态
          clockTime: `打卡时间：2024-07-10 10:00:00`, // 标注点时间
          showWindow: true // 是否显示窗口
        }
      };
    } else {
      return {
        id: item.pointId,
        styleId: "routeItem",
        position: new window.TMap.LatLng(coordinate[1], coordinate[0]), // 标注点位置
        properties: {
          // 标注点属性
          name: `位置：${item.pointName}`, // 标注点名称
          positionStatus: `点位状：已打卡`, // 打卡状态
          clockTime: `打卡时间：2024-07-10 10:00:00`, // 标注点时间
          showWindow: true // 是否显示窗口
        }
      };
    }
  });
  return newList;
};
const setAllPoints = () => {
  let list = getAllPoints(allPoints);
  emits("setAllPoints", list);
};
const resetMarkers = () => {
  emits("resetMarkers");
};
const setRoute = () => {
  let list = getRoutePoints(routePoints);
  console.log(list);
  let currentL = getAllPoints(currentPostion, "currentPostion");
  let cameraL = getAllPoints(cameraPostion, "camera");
  let redL = getAllPoints(redWarning, "redWarning");
  let greenL = getAllPoints(greenWarning, "greenWarning");
  let newList = [...list, ...cameraL, ...currentL, ...redL, ...greenL];
  emits("setAllPoints", newList);
  emits("setRoute", routePlan);
};
const clearRoute = () => {
  emits("resetMarkers");
  emits("clearRoute");
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.text-container {
  position: relative;
  top: 10%;
  left: 10px;
  z-index: 2;
}
</style>
