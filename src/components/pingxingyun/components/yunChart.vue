<template>
  <div class="yuniframe">
    <!-- 引入平行云iframe -->
    <iframe class="pad" id="iframe" src="" width="100%" scrolling="no" frameborder="0" ref="modelframe"></iframe>
  </div>
</template>
<script setup>
import { getConfig } from "@/api/screen";
import { onMounted, ref } from "vue";
import { event } from "../event.js";
//import { toggle } from "./fullScreen.js";
import useDraw from "@/utils/useDraw";
const { calcRateYun } = useDraw();
const props = defineProps({
  curAppId: {
    type: String,
    default: ""
  }
});
const emits = defineEmits(["asyncResize"]);
const timeout = ref(null);
const poster = ref(null);
const modelframe = ref(null);

const serverAddr = "**************:8181";
const isFull = ref(false);
const doc = {
  value: null
};

const config = {
  userLocalIP: true,
  server: "", // server
  testAppId: "",
  testAppUrl: ""
};
const initConfig = () => {
  getConfig({ configKey: "modelDomain" }).then(res => {
    let serverAddr = "";
    let testAppId = "";
    if (res.rows?.length) {
      const { server, appId } = JSON.parse(res.rows[0].configValue);
      serverAddr = server;
      testAppId = appId;
    }
    config.server = `http://${serverAddr}`;
    config.testAppId = testAppId;
    config.testAppUrl = `http://${serverAddr}/webclient/?appliId=${testAppId}`;
    init();
    poster.value = new lark.iframePoster(modelframe.value, {
      onMessage: onMessage,
      listenKeyboard: true
    });
  });
};
onMounted(() => {
  //初始化
  initConfig();
  event.on("init", info => {
    sendText(JSON.stringify(info).trim());
  });
  //初始化
  startSendClientWindowSize();

  //页面切换
  event.on("changePage", info => {
    sendText(JSON.stringify(info).trim());
  });
  //漫游方案
  event.on("changeRoam", info => {
    sendText(JSON.stringify(info).trim());
  });

  //停车场
  event.on("changeView", info => {
    handleQuery(info);
  });
  //初始化停车场所有车位信息
  event.on("initParkingInfo", info => {
    sendText(JSON.stringify(info).trim());
  });
  //地面闸机来车
  event.on("enterCar", info => {
    sendText(JSON.stringify(info).trim());
  });
  //停车场信息更新
  event.on("parkingInfoUpdate", info => {
    sendText(JSON.stringify(info).trim());
  });
  //停车场显示有车还是无车
  event.on("showCamera", info => {
    sendText(JSON.stringify(info).trim());
  });
  //停车场区域
  event.on("selectArea", info => {
    sendText(JSON.stringify(info).trim());
  });
  /*-----------------------------------------设备监控---------------------------------*/
  //点击侧边栏
  event.on("onClickDeviceSidebar", info => {
    sendText(JSON.stringify(info).trim());
  });
  //点击设备类型
  event.on("onClickDevicesType", info => {
    sendText(JSON.stringify(info).trim());
  });
  //电梯信息更新
  event.on("updateElevatorInfo", info => {
    sendText(JSON.stringify(info).trim());
  });
  /*-----------------------------------------工单管理---------------------------------*/
  //工单点击遥控录像
  event.on("onClickWorkorderRoute", info => {
    sendText(JSON.stringify(info).trim());
  });
  //点击工单状态
  event.on("onClickWorkorderType", info => {
    sendText(JSON.stringify(info).trim());
  });
  /*-----------------------------------------空间管理---------------------------------*/
  //选择查看楼层内房间及类型
  event.on("selectFloor", info => {
    sendText(JSON.stringify(info).trim());
  });
  //环境告警通知
  event.on("roomAlarm", info => {
    sendText(JSON.stringify(info).trim());
  });

  /*----------------------------------智慧安防----------------------------------------*/
  //事件告警通知
  //设备类型通知
  event.on("safetyDeviceTypes", info => {
    sendText(JSON.stringify(info).trim());
  });
  //单击设备告警或者事件告警
  event.on("onDeviceAlarm", info => {
    sendText(JSON.stringify(info).trim());
  });
});
const handleQuery = str => {
  sendText(
    JSON.stringify({
      eventName: "changeView",
      args: str ? str : "exterior"
    }).trim()
  );
};
const init = () => {
  if (!config.server) {
    alert("请设置 config.server");
    return;
  }
  if (config.testAppUrl) {
    modelframe.value.src = config.testAppUrl;
    //.$el.attr("src", config.testAppUrl);
  } else if (config.testAppId) {
    //enterApp(config.testAppId);
  }
};

const isJsonString = str => {
  try {
    let obj = JSON.parse(str);
    if (obj && typeof obj == "object") return true;
  } catch {
    return false;
  }
  return false;
};
const onMessage = e => {
  let info = null;

  switch (e.data.type) {
    // open
    case 20200:
      console.log("通道开启", e.data.data);
      // 同步客户端窗口大小
      event.emit("load", false);
      //clickevent();
      // info = { Mode: "楼层切换重置" };
      // sendText(JSON.stringify(info).trim());
      syncClientWindowSize();
      poster.value?.setShowControlBall(false);
      //startSendClientWindowSize();
      break;
    case 20201:
      console.log("通道关闭", e.data.data);
      break;
    // 接收到字节消息
    case 20202:
      console.log("接收到字节消息", e.data.data);
      break;
    // 接收到文本消息
    case 20203:
      console.log("接收到文本消息", e.data.data);

      if (e.data.data == "三维加载完成") {
        event.emit("load", false);
      } else {
        const data = isJsonString(e.data.data) ? JSON.parse(e.data.data) : e.data.data;
        console.log(isJsonString(e.data.data), data);
        data?.eventName && event.emit(data.eventName, data);
      }

      break;
    default:
      break;
  }
};

// 发送字符消息。
const sendText = jsonStr => {
  console.log(jsonStr, 11);
  poster.value?.sendTextToRenderSererAppDataChannel(jsonStr);
};

// 发送字节消息
// const sendBinary = binary => {
//   poster.value?.sendBinaryToRenderServerAppDataChannel(binary);
// };

const startSendClientWindowSize = () => {
  window.addEventListener("resize", function () {
    if (timeout.value != null) {
      window.clearTimeout(timeout);
    }
    // emits("asyncResize");
    timeout.value = window.setTimeout(function () {
      syncClientWindowSize();
    }, 500);
  });
};
const syncClientWindowSize = () => {
  let size = {};
  if (isFull.value) {
    size = {
      width: window.screen.width,
      height: window.screen.height + 50
    };
  } else {
    if (document.compatMode === "BackCompat") {
      size = {
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      };
    } else {
      size = {
        width: document.documentElement.clientWidth,
        height: document.documentElement.clientHeight
      };
    }
  }

  sendText(
    JSON.stringify({
      eventName: "resizeWindow",
      args: {
        width: size.width,
        height: size.height
      }
    })
  );
};
defineExpose({
  startSendClientWindowSize
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.yuniframe {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;

  z-index: 1;
}
$loaderHeight: 180px;
.pad {
  height: 99.4%;
}
// @media (min-width: 768px) and (max-width: 1024px) {
//   .pad {
//     height: 126%;
//   }
// }
</style>
