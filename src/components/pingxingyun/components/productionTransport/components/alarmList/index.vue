<template>
  <el-card>
    <div class="modelTitle flex aligns_center spacec">
      <img :src="alarmImg" alt="" />
      <span>告警列表</span>
    </div>
    <el-scrollbar max-height="68vh">
      <div ref="tableRef" class="setTableStyle" style="overflow: auto">
        <div v-if="tableData.length" v-infinite-scroll="loadData" :infinite-scroll-disabled="disabled">
          <Item
            v-for="item in tableData"
            class="itemStyle"
            :content="item"
            :key="item.alarmId"
            type="alarm"
            status="disposeStatus"
            @handleCard="handleCard"
            :class="curCardId == item.alarmId ? 'itemActive' : 'itemUnActive'"
          >
            <template #title="{ row }">
              <div class="setAllInfo">
                {{ row.strategyName }}
              </div>
            </template>
            <template #edit="{ row }"> </template>
          </Item>
        </div>
        <el-empty description="暂无数据" v-else></el-empty>
        <p v-if="loading" class="text-center">加载中...</p>
        <p v-if="noMore" class="text-center">到底了</p>
      </div>
    </el-scrollbar>
  </el-card>
</template>
<script setup>
import { queryTodayAlarmList } from "@/api/screen/product.js";
import { event } from "@/components/pingxingyun/event.js";
import { requireImg } from "@/utils/ruoyi";
import { onMounted, shallowRef } from "vue";
import Item from "../item";
const initData = {
  alarmType: 2,
  pageNum: 1,
  pageSize: 10
};
const queryData = ref({ ...initData });
const groupTypes = shallowRef([]);
const tableRef = ref(null);
const curCardId = ref("");
const tableData = shallowRef([]);
const total = ref(0);
const loading = ref(false);
const noMore = computed(() => total.value && tableData.value.length == total.value); //
const disabled = computed(() => loading.value || noMore.value);
const alarmImg = requireImg("production/alarm.png");

const getTableData = async () => {
  try {
    const data = await queryTodayAlarmList();
    tableData.value = tableData.value.concat(data.rows);
    total.value = data.total;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};
const loadData = () => {
  loading.value = true;

  queryData.pageNum++;
  getTableData();
};

//getWorkTypes();
//单击整个卡片
const handleCard = val => {
  //重复点击
  if (curCardId.value === val.alarmId) {
    curCardId.value = "";
  } else {
    curCardId.value = val.alarmId;
  }
  event.emit("alarmInfo", { id: curCardId.value, alarmType: 2 }); //通知顶级包含块该事件
};
const handleQuery = () => {
  queryData.value.pageNum = 1;
  tableData.value = [];
  getTableData();
};
onMounted(() => {
  handleQuery();
  event.on("clearCurAlarmId", () => {
    if (curCardId.value) curCardId.value = "";
  });
  // observe = new IntersectionObserver(callback, {
  //   root: tableRef.value,
  //   threshold: [0, 1]
  // });

  // observe.observe(loadRef.value);
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.modelTitle {
  font-size: vw(20);
  padding-bottom: vh(26);
  img {
    width: vw(20);
  }
}
.itemActive {
  background-color: rgba(229, 228, 228, 0.9);
}
.itemUnActive {
  background-color: rgba(229, 228, 228, 0.3);
}
.setTableStyle {
  padding: 1vh 0;

  font-size: $vw14;
  .itemStyle {
    margin-bottom: 1vh;
  }

  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
</style>
