<template>
  <el-card>
    <div class="text-center modelTitle">今日生产运输任务</div>
    <div>
      <el-form :model="queryData" @submit.prevent>
        <el-form-item label="作业类型">
          <el-select v-model="queryData.kindId" clearable placeholder="请选择作业类型" @change="handleQuery" style="width: 100%">
            <el-option v-for="item in groupTypes" :key="item.id" :label="item.kindName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务名称">
          <el-input v-model="queryData.taskName" clearable @change="handleQuery" suffix-icon="Search" />
        </el-form-item>
      </el-form>
    </div>
    <el-scrollbar max-height="68vh">
      <div ref="tableRef" class="setTableStyle">
        <div v-if="tableData.length" v-infinite-scroll="loadData" :infinite-scroll-disabled="disabled">
          <Item
            v-for="item in tableData"
            class="itemStyle"
            :class="curCardId == item.taskId ? 'itemActive' : 'itemUnActive'"
            :content="item"
            :key="item.taskId"
            type="work"
            @handleCard="handleCard"
            status="status"
          >
            <template #title="{ row }">
              <div>
                <span>{{ row.workerName || "" }}</span
                ><span>{{ row.carNo ? `-${row.carNo}` : "" }}</span>
              </div>
            </template>
            <template #edit="{ row }"> <img :src="editImg" alt="" srcset="" @click.stop="handleEdit(row)" class="editStyle" /></template>
          </Item>
        </div>
        <el-empty description="暂无数据" v-else></el-empty>
        <p v-if="loading" class="text-center">加载中...</p>
        <p v-if="noMore" class="text-center">到底了</p>
      </div>
    </el-scrollbar>
  </el-card>
</template>
<script setup>
import { queryAllCarLocation, queryTaskList, queryWorkType } from "@/api/screen/product.js";
import { getAllPoints } from "@/components/pingxingyun/components/setMapDataMethod.js";
import { event } from "@/components/pingxingyun/event.js";
import { requireImg } from "@/utils/ruoyi";
import { onBeforeUnmount, onMounted, shallowRef } from "vue";
import Item from "../item";
const initData = {
  kindId: null,
  taskName: null,
  pageNum: 1,
  pageSize: 10
};
const queryData = ref({ ...initData });
const groupTypes = shallowRef([]);
const tableRef = ref(null);
const curCardId = ref(null); // 当前被点击的卡片
const tableData = shallowRef([]);
const total = ref(0);
const loading = ref(false);
const noMore = computed(() => total.value && tableData.value.length == total.value); //
const disabled = computed(() => loading.value || noMore.value);
const editImg = requireImg("production/update.png");
let setAllCarLocation;
const getWorkTypes = async () => {
  const data = await queryWorkType({ isBus: "0" });
  if (data.data?.length > 0) {
    groupTypes.value = data.data;
  }
};
//获取所有车的位置点；
const getAllWorkCar = async () => {
  const res = await queryAllCarLocation();
  const data = res.data.reduce((pre, next) => {
    const location = next.location;
    pre.push({
      pointId: next.id,
      carNo: next.carNo,
      coordinate: [location.lng || location.lng, location.lat || location.lat],
      location: { direction: location.direction }
    });
    return pre;
  }, []);

  const list = getAllPoints(data);

  event.emit("showAllCarMarkers", list);
};

const getTableData = async () => {
  try {
    if (setAllCarLocation) {
      clearInterval(setAllCarLocation);
      setAllCarLocation = null;
    }
    const data = await queryTaskList(queryData.value);

    tableData.value = tableData.value.concat(data.rows);
    total.value = data.total;
    loading.value = false;

    if (data.total) {
      setAllCarLocation = setInterval(getAllWorkCar, 60000);
    }
  } catch (error) {
    loading.value = false;
  }
};
const loadData = () => {
  loading.value = true;
  queryData.pageNum++;
  getTableData();
};
// const callback = entries => {
//   console.log(111);
//   if (entries[0].intersectionRatio <= 0) return;
//   if (tableData.value.length == total.value) return;
//   queryData.pageNum++;
//   handleQuery();
// };
//let observe = null;
getWorkTypes();
getAllWorkCar();
//单击整个卡片
const handleCard = val => {
  //重复点击
  if (curCardId.value === val.taskId) {
    curCardId.value = "";
    //通知地图展示展示说有的车辆当前的点位，并隐藏掉已经显示的路线,同时添加所有车辆的位置，并去除当前车辆位置的轮巡
    getAllWorkCar();

    //event.emit("showAllCar");
  } else {
    //通知地图展示新的车辆的路线，并隐藏掉所有的车辆当前的点位
    if (setAllCarLocation) {
      clearInterval(setAllCarLocation);
      setAllCarLocation = null;
    }
    curCardId.value = val.taskId;
  }
  event.emit("workInfo", { taskId: curCardId.value, status: val.status, carNo: val.carNo }); //通知顶级包含块该作业信息的展示与隐藏
};
const handleEdit = val => {
  event.emit("editWork", { isBus: 0, currentId: val.taskId }); //通知顶级包含块该事件
};
const handleQuery = () => {
  queryData.value.pageNum = 1;
  tableData.value = [];
  getTableData();
};
onBeforeUnmount(() => {
  //清空轮巡

  clearInterval(setAllCarLocation);
  setAllCarLocation = null;
});
onMounted(() => {
  handleQuery();
  event.on("handleOperate", val => {
    //重新获取所有数据
    handleQuery();
  });

  // observe = new IntersectionObserver(callback, {
  //   root: tableRef.value,
  //   threshold: [0, 1]
  // });

  // observe.observe(loadRef.value);
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.modelTitle {
  font-size: vw(20);
  padding-bottom: vh(26);
}
.itemActive {
  background-color: rgba(229, 228, 228, 0.9);
}
.itemUnActive {
  background-color: rgba(229, 228, 228, 0.3);
}
.editStyle {
  margin-right: 0.5vw;
  width: 1vw;
  height: 1vw;
}
// .setTableStyle:hover {
//   &::-webkit-scrollbar {
//     width: 4px;
//     height: 16px;
//   }
// }

.setTableStyle {
  padding: 1vh 0;

  font-size: $vw14;
  // overflow: auto;

  .itemStyle {
    margin-bottom: 1vh;
  }
  .loaderStyle {
    padding: 0.5vh 0;
    font-size: $vw16;
    color: #999;
  }

  &::-webkit-scrollbar {
    width: 4px;
    height: 16px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}
</style>
