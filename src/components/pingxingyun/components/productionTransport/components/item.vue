<template>
  <div class="item_content" @click="handle">
    <div class="flex spaceb item_header aligns_center">
      <slot name="title" :row="content"></slot>
      <div class="flex aligns_center">
        <slot name="edit" :row="content"></slot>
        <el-tag :color="queryStatus().bgColor" effect="dark" style="border-width: 0px">
          {{ queryStatus().text }}
        </el-tag>
      </div>
    </div>
    <div class="item_body">
      <div v-for="(item, index) in queryItems()" :key="index" class="flex itemStyle aligns_center">
        <div class="setItemName">{{ item.name }}:</div>

        <div class="setAllInfo setItemValue">{{ content[item.prop] }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { getItems, getStatus } from "../list_config.js";
const props = defineProps({
  content: {
    type: Object,
    defautlt: () => {}
  },
  type: {
    type: String,
    defautlt: () => ""
  },
  status: {
    type: String,
    defautlt: () => "status"
  }
});
const emits = defineEmits(["handleCard"]);
const handle = () => {
  emits("handleCard", props.content);
};
const queryStatus = () => {
  return getStatus(props.type, props.content[props.status]);
};
const queryItems = () => {
  return getItems(props.type, props.content[props.status]);
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.item_content {
  padding: 1vh 0.5vw;
  border-radius: 5px;
  cursor: pointer;
  .item_header {
    font-size: $vw16;
  }
}
.item_content:hover {
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);

  transition: all 0.3s ease;
}
.setAllInfo {
  white-space: normal;
  overflow-wrap: break-word;
}
.item_body {
  font-size: $vw14;
  margin-top: 1vh;
  color: #666;
  .itemStyle {
    margin-bottom: 0.5vh;
  }
}
.setItemName {
  // width: vw(100);
  margin-right: vw(8);
  flex-shrink: 0;
}
.setItemValue {
}
</style>
