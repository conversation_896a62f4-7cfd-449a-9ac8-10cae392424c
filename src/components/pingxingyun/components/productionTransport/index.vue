<template>
  <div class="container_box">
    <div class="leftmodel poi_abs z-in2">
      <TaskList></TaskList>
    </div>
    <div class="operateButton poi_abs z-in2">
      <div class="pointer animate_cursor containBlock" @click="operatetopButton">新增作业</div>
    </div>
    <div class="rightmodel poi_abs z-in2" :class="{ transformLeft: isRightCollapsed }" ref="rightModelRef">
      <AlarmList></AlarmList>
    </div>
    <!-- 收起/展开按钮 -->
    <div class="toggle-button poi_abs z-in2" @click="toggleRightPanel" ref="toggleButtonRef" :style="buttonStyle">
      <i :class="isRightCollapsed ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"></i>
    </div>
  </div>
</template>
<script setup>
import { event } from "@/components/pingxingyun/event.js";
import { computed, nextTick, onMounted, ref } from "vue";
import { default as AlarmList } from "./components/alarmList";
import { default as TaskList } from "./components/task";

// 控制右侧面板收起状态
const isRightCollapsed = ref(false);
// DOM 引用
const rightModelRef = ref(null);
const toggleButtonRef = ref(null);

const operatetopButton = () => {
  event.emit("addNewWork", { isBus: "0" });
};

// 切换右侧面板收起/展开状态
const toggleRightPanel = () => {
  isRightCollapsed.value = !isRightCollapsed.value;
};

// 动态计算按钮位置
const buttonStyle = computed(() => {
  return {};
});

// 计算按钮位置的函数
const calculateButtonPosition = () => {
  if (rightModelRef.value && toggleButtonRef.value) {
    const rightModelRect = rightModelRef.value.getBoundingClientRect();
    const rightModelHeight = rightModelRect.height;
    const rightModelTop = rightModelRect.top;

    // 计算按钮应该在的位置：rightmodel 的顶部 + 高度的一半
    const buttonTop = rightModelTop + rightModelHeight / 2;

    // 更新按钮样式
    toggleButtonRef.value.style.top = `${buttonTop}px`;
  }
};

// 监听窗口大小变化和内容变化
const setupResizeObserver = () => {
  if (rightModelRef.value) {
    const resizeObserver = new ResizeObserver(() => {
      nextTick(() => {
        calculateButtonPosition();
      });
    });
    resizeObserver.observe(rightModelRef.value);

    // 监听窗口大小变化
    window.addEventListener("resize", calculateButtonPosition);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", calculateButtonPosition);
    };
  }
};

onMounted(() => {
  nextTick(() => {
    calculateButtonPosition();
    setupResizeObserver();
  });
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.operateButton {
  top: vh(100);
  left: calc(0.5vw + vw(344));
}

.toggle-button {
  // top 位置由 JavaScript 动态计算
  right: calc(vw(22) + vw(320) - vw(15)); // rightmodel的最左侧减去按钮宽度的一半
  width: vw(30);
  height: vw(30);
  background: rgba(59, 80, 124, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  font-size: vw(16);
  transition: all 0.3s ease;
  transform: translateY(-50%); // 垂直居中

  &:hover {
    background: rgba(59, 80, 124, 1);
    transform: translateY(-50%) scale(1.1);
  }
}

// 当右侧面板收起时，调整按钮位置
.rightmodel.transformLeft + .toggle-button {
  right: calc(vw(22) - vw(15)); // 面板收起时，按钮移到屏幕右边缘减去按钮宽度的一半

  &:hover {
    transform: translateY(-50%) scale(1.1);
  }
}
</style>
