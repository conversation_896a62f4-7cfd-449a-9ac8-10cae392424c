const stausMap = {
  work: {
    0: {
      text: "未分配",
      status: "0",
      color: "#fff",
      bgColor: "#f9ae3d"
    },
    1: {
      text: "未开始",
      status: "1",
      color: "#fff",
      bgColor: "#E56B19"
    },
    2: {
      text: "进行中",
      status: "2",
      color: "#fff",
      bgColor: "#aa001e"
    },
    3: {
      text: "已结束",

      status: "3",
      color: "#fff",
      bgColor: "#909193"
    },
    4: {
      text: "已取消",
      status: "4",
      color: "#fff",
      bgColor: "#909193"
    },
    5: {
      text: "已失效",
      status: "5",
      color: "#fff",
      bgColor: "#909193"
    }
  },
  alarm: {
    0: {
      text: "未处理",
      status: "0",
      color: "#fff",
      bgColor: "#f9ae3d"
    },
    1: {
      text: "处理中",
      status: "1",
      color: "#fff",
      bgColor: "#aa001e"
    },
    2: {
      text: "已处理",
      status: "2",
      color: "#fff",
      bgColor: "#909193"
    }
  }
};

const formItems = {
  work: [
    {
      prop: "taskName",
      status: [0, 1, 2, 3, 4, 5, 6],
      name: "作业名称"
    },
    {
      prop: "startTime",
      status: [0, 1, 2, 4, 5, 6],
      name: "计划开始时间"
    },
    {
      prop: "endTime",
      status: [0, 1, 2, 4, 5, 6],
      name: "计划结束时间"
    },

    {
      prop: "startTime",
      status: [3],
      name: "任务执行时间"
    },
    {
      prop: "endTime",
      status: [3],
      name: "任务结束时间"
    }
  ],
  alarm: [
    {
      prop: "disposeExplain",
      status: [0, 1, 2],
      name: "告警内容"
    },
    {
      prop: "alarmTime",
      status: [0, 1, 2],
      name: "告警时间"
    },
    {
      prop: "verdictTime",
      status: [2],
      name: "处理时间"
    }
  ]
};
export const getStatus = (type, key) => {
  return stausMap[type][key];
};
export const getItems = (type, key) => {
  return formItems[type].filter(item => item.status.includes(Number(key)));
};
