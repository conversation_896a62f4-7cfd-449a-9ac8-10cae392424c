<template>
  <el-dialog v-model="visable" :title="title" destroy-on-close append-to-body :width="dialogWidth">
    <el-form ref="formRef" :model="form" :rules="rules" :label-width="toRem('7rem')">
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="作业名称" prop="taskName">
            <el-input v-model="form.taskName" placeholder="请输入内容" maxlength="50" show-word-limit :disabled="!!currentId" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业类型" prop="kindId">
            <el-select v-model="form.kindId" placeholder="请选择" clearable style="width: 100%" :disabled="!!currentId">
              <el-option v-for="dict in kindList" :key="dict.id" :label="dict.kindName" :value="dict.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业班组" prop="groupId">
            <el-select v-model="form.groupId" placeholder="请选择" clearable style="width: 100%">
              <el-option v-for="dict in groupList" :key="dict.groupId" :label="dict.groupName" :value="dict.groupId" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="作业人员" prop="currentEmployeeId">
            <el-select v-model="form.currentEmployeeId" placeholder="请选择" clearable style="width: 100%">
              <el-option v-for="dict in personList" :key="dict.userId" :label="dict.employeeName" :value="dict.userId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm"
              date-format="YYYY-MM-DD"
              time-format="HH:mm"
              style="width: 100%"
              placeholder="请选择作业开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              value-format="YYYY-MM-DD HH:mm"
              farmat="YYYY-MM-DD HH:mm"
              date-format="YYYY-MM-DD"
              time-format="HH:mm"
              style="width: 100%"
              placeholder="请选择作业开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="16" v-show="!currentId">
          <el-form-item label="作业路线" prop="routeId" :rules="{ required: !!currentId ? false : true, message: '请选择作业路线', trigger: 'blur' }">
            <div class="flex spaceb" style="width: 100%">
              <el-input placeholder="请点击选择路线" readonly v-model="labelName">
                <template #append><el-button type="primary" plain @click="chooseLine">选择路线</el-button></template>
              </el-input>
              <el-button type="primary" class="ml10" v-if="form.routeId" plain @click="reverseLine">路线翻转</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-show="!currentId">
          <el-form-item label="">
            <el-table :data="pointTableList" max-height="350">
              <el-table-column type="index" label="序号" :width="toSclePx(60)" align="center" />
              <el-table-column
                v-for="(item, index) in columns"
                :label="item.label"
                :width="item.width"
                :align="item.align"
                :prop="item.prop"
                :key="index"
              />
              <el-table-column label="点位类型" align="center" prop="pointType">
                <template #default="scope">
                  {{ scope.row.pointType == 1 ? "任务点" : "打卡点" }}
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog title="作业路线" v-model="selectRouteShow" destroy-on-close append-to-body :width="dialogWidth">
      <el-form ref="workFormRef" :model="dialogForm" :inline="true" @submit.prevent>
        <el-form-item label="路线名称" prop="routeName">
          <el-input
            v-model="dialogForm.routeName"
            :style="{ width: toRem('14rem') }"
            maxlength="50"
            show-word-limit
            placeholder="请输入内容"
            clearable
            @clear="searchWorkLine"
            @keyup.enter="searchWorkLine"
          />
        </el-form-item>

        <el-form-item label="">
          <el-button type="primary" icon="Search" @click="searchWorkLine">搜索</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="routesLoading" :data="routesTableList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column
          v-for="item in columnsDialog"
          :label="item.label"
          :width="item.width"
          :align="item.align"
          :prop="item.prop"
          :key="item.label"
        />
        <el-table-column label="是否为公交路线" align="center" prop="isEnable">
          <template #default="scope">
            {{ scope.row.isBus == 1 ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column label="路线描述" align="center" prop="routeDesc" show-overflow-tooltip width="200" />
      </el-table>
      <pagination
        :style="{ 'margin-bottom': toRem('1rem') }"
        v-show="totalLine > 0"
        :total="totalLine"
        v-model:page="dialogForm.pageNum"
        v-model:limit="dialogForm.pageSize"
        @pagination="getRouteListFunc"
      />
      <div class="addFooterInItem">
        <el-button @click="selectRouteShow = false">取消</el-button>
        <el-button type="primary" @click="submitDialog">确定</el-button>
      </div>
    </el-dialog>
    <div class="addFooterInItem">
      <el-button @click="visable = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </el-dialog>
</template>
<script setup>
import { getRouteById, getRouteList, queryWorkType } from "@/api/screen/product.js";

import { getTaskInfo, queryTeamInfoRequest, submitAddTask, submitUpdateTask } from "@/api/screen/work.js";
import { event } from "@/components/pingxingyun/event.js";
import { getCurrentInstance, nextTick, reactive, shallowRef, watch } from "vue";
import useForm from "./useForm";
const { proxy } = getCurrentInstance();
const props = defineProps({
  dialogWidth: {
    type: String,
    default: () => "50%"
  },

  groupList: {
    type: Array,
    default: () => []
  }
});
const initFormData = {
  taskName: null,
  kindId: null,
  currentEmployeeId: "",
  groupId: null,
  startTime: null,
  endTime: null,
  routeId: null,
  routeReversal: "0"
};
const initRule = {
  taskName: [{ required: true, message: "请填写作业名称", trigger: "blur" }],
  currentEmployeeId: [{ required: true, message: "请选择作业人员", trigger: ["blur", "change"] }],
  groupId: [{ required: true, message: "请选择作业班组", trigger: ["blur", "change"] }],
  kindId: [{ required: true, message: "请选择作业类型", trigger: ["blur", "change"] }],
  endTime: [{ required: true, message: "请选择作业结束时间", trigger: ["blur", "change"] }],
  startTime: [{ required: true, message: "请选择作业开始时间", trigger: ["blur", "change"] }]
};
const { formRef, form, rules, reset } = useForm(initFormData, initRule);
const visable = ref(false);
const selectRouteShow = ref(false);
const title = ref("");
const personList = shallowRef([]);
const kindList = shallowRef([]);
const dialogForm = reactive({
  routeName: "",
  isBusRoute: "0",
  pageNum: 1,
  pageSize: 10
});
const columns = [
  { prop: "pointName", label: "点位名称", align: "center" },
  { prop: "pointCode", label: "点位编码", align: "center" },
  { prop: "coordinate", label: "点位坐标", align: "center", width: "220" }
];
const columnsDialog = [
  { prop: "routeName", label: "路线名称", align: "center" },
  { prop: "routeCode", label: "路线编码", align: "center" }
];
const currentId = ref(null);
const routesTableList = ref([]);
const pointTableList = ref([]);
const totalLine = ref(0);
const routesLoading = ref(false);

watch(
  () => form.value.groupId,
  newVal => {
    personList.value = [];
    form.value.currentEmployeeId = "";
    if (newVal) {
      getPersonList(newVal);
    }
  }
);
// 获取人员列表
const getPersonList = async groupId => {
  const res = await queryTeamInfoRequest(groupId);
  if (res.data) {
    personList.value = res.data?.dispatchEmployeeList || [];
  }
};
// 获取作业类型
const getKindList = async val => {
  const res = await queryWorkType({ isBus: val });
  if (res.code == 200) {
    kindList.value = res.data || [];
  }
};
// 查询作业路线的表格
const searchWorkLine = () => {
  dialogForm.pageNum = 1;
  getRouteListFunc();
};
const getRouteListFunc = () => {
  routesLoading.value = true;
  const params = {
    kindId: form.value.kindId,
    ...dialogForm
  };
  getRouteList(params).then(res => {
    if (res.code == 200) {
      routesLoading.value = false;
      routesTableList.value = res.rows;
      totalLine.value = res.total || 0;
    }
  });
};
// 多选
const selectList = ref([]);
const handleSelectionChange = val => {
  selectList.value = val;
};
// 保存路线
const labelName = ref(null);
const submitDialog = () => {
  if (selectList.value && selectList.value.length > 1) {
    return proxy?.$modal.msgWarning("只允许保存一条路线");
  }

  form.value.routeId = selectList.value[0].routeId;
  form.value.routeReversal = "0";
  getRouteByIdFunc(form.value.routeId);
  selectRouteShow.value = false;
};
// 根据id查询路线
const getRouteByIdFunc = async id => {
  await getRouteById(id).then(res => {
    if (res.code == 200) {
      pointTableList.value = form.value.routeReversal == "1" ? res.data.routePoints.reverse() : res.data.routePoints;
      labelName.value = res.data.routeName || "";
    }
  });
};
// 选择路线
const chooseLine = () => {
  getRouteListFunc();
  selectRouteShow.value = true;
};
// 路线翻转
const reverseLine = () => {
  form.value.routeReversal = form.value.routeReversal == "1" ? "0" : "1";
  pointTableList.value.reverse();
};
//传值截取
const sliceTime = val => {
  return val.slice(0, val.length - 3);
};
// 保存
const submitForm = () => {
  formRef.value.validate(async valid => {
    if (valid) {
      form.value.startTime = sliceTime(form.value.startTime);
      form.value.endTime = sliceTime(form.value.endTime);
      form.value.userId = form.value.currentEmployeeId;
      const res = currentId.value ? await submitUpdateTask(form.value) : await submitAddTask(form.value);

      if (res.code == 200) {
        proxy?.$modal.msgSuccess("保存成功");
        event.emit("handleOperate", currentId.value);
        visable.value = false;
      }
    }
  });
};
const openDialog = async val => {
  visable.value = true;
  currentId.value = val?.currentId || "";
  title.value = currentId.value ? "编辑作业" : "新增作业";
  pointTableList.value = [];
  labelName.value = "";

  getKindList(val.isBus);
  reset();
  if (val.currentId) {
    //处理编辑逻辑

    const res = await getTaskInfo(currentId.value);
    form.value = { ...res.data };
    nextTick(() => {
      form.value.currentEmployeeId = res.data.currentEmployeeId;
    });
  }
};
defineExpose({ openDialog });
</script>
<style>
.el-overlay {
  background-color: unset;
}
</style>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
</style>
