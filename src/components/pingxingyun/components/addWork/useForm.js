const useForm = (...parameter) => {
  const [formDatas, rule, proxy] = parameter;
  const formRef = ref(null);
  const loading = ref(false);
  const data = reactive({
    form: {
      ...formDatas
    },
    rules: { ...rule }
  });
  const initFormData = { ...formDatas };
  const imageUrl = ref("");
  const { form, rules } = toRefs(data);
  const reset = call => {
    form.value = { ...initFormData };
    formRef.value?.resetFields();
    call && call();
  };

  return {
    formRef,
    imageUrl,
    form,
    rules,
    loading,
    reset
  };
};
export default useForm;
