<template>
  <div class="poi_abs z-in2 workRight">
    <el-card style="margin-bottom: 1vh">
      <div class="flex spaceb aligns_center everyTitle">
        <div>告警详情</div>
        <!-- <el-tag color="#aa001e" effect="dark" style="border-width: 0px"> 处理流程 </el-tag> -->
      </div>

      <div>
        <div v-for="item in alarmColumns" :label="item.label" :prop="item.prop" :key="item.prop" class="flex everyItem">
          <div class="setLabelStyle">{{ item.label }}:</div>
          <div class="setAllInfo">
            {{ item.value || "暂无" }}
          </div>
        </div>
      </div>
    </el-card>
    <el-card style="margin-bottom: 1vh">
      <div class="everyTitle">前行车记录仪告警详情</div>
      <div>
        <VideoPlayer ref="forwordViedo"></VideoPlayer>
      </div>
    </el-card>
    <el-card>
      <div class="everyTitle">后行车记录仪告警详情</div>
      <div>
        <VideoPlayer ref="behindViedo"></VideoPlayer>
      </div>
    </el-card>
  </div>
</template>
<script setup>
import { getAlarmDetails } from "@/api/screen/product";
import VideoPlayer from "@/components/pingxingyun/components/videoPlayer";
import { shallowRef } from "vue";
import { formAlarmList } from "./formList";
const alarmStatusMap = {
  0: "未处理",
  1: "处理中",
  2: "已处理"
};
const props = defineProps({
  cartTypes: {
    type: Array,
    default: () => []
  }
});
const formAlarmDate = ref({});
const alarmColumns = shallowRef([]);
const forwordViedo = shallowRef(null);
const behindViedo = shallowRef(null);
const queryNewWork = async val => {
  const res = await getAlarmDetails(val.id);
  formAlarmDate.value = res.data;

  alarmColumns.value = [...formAlarmList];

  alarmColumns.value.forEach(item => {
    item.value = formAlarmDate.value[item.prop];
    if (item.prop == "alarmLevel") {
      item.value = formAlarmDate.value["alarmLevel"] == 2 ? "严重" : "警告";
    }
    if (item.prop == "disposeStatus") {
      item.value = alarmStatusMap[formAlarmDate.value[item.prop]];
    }
  });
  const vedioBack = await getAlarmDetails(val.id);
  if (vedioBack.data?.length) {
    forwordViedo.value?.playVideo(vedioBack.data[0].recorderCode);
    if (vedioBack.data.length > 1) behindViedo.value?.playVideo(vedioBack.data[1].recorderCode);
  }
};
const destroy = () => {
  forwordViedo.value?.destroyFlv();
  behindViedo.value?.destroyFlv();
};
defineExpose({ queryNewWork, destroy });
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.workRight {
  width: 14vw;
  top: vh(100);
  right: calc(0.5vw + vw(344));
  font-size: $vw14;
  img {
    width: 3vw;
  }
  .everyTitle {
    font-size: $vw16;
    font-weight: 600;
    margin-bottom: 1vh;
  }
  .everyItem {
    margin-bottom: 0.5vh;
    .setLabelStyle {
      width: 3.5vw;
      color: #666;
      flex-shrink: 0;
    }
    .setAllInfo {
      white-space: normal;
      overflow-wrap: break-word;

      color: #666;
    }
  }
}
::v-deep .el-card {
  // background-color: rgba(141 92 139 / 50%);
}
</style>
