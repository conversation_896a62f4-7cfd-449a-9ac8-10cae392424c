<template>
  <div class="container_box">
    <div class="leftmodel poi_abs z-in2">
      <TaskList></TaskList>
    </div>
    <div class="operateButton poi_abs z-in2">
      <div class="pointer animate_cursor containBlock" @click="operatetopButton">新增作业</div>
    </div>
    <div class="rightmodel poi_abs z-in2">
      <AlarmList></AlarmList>
    </div>
  </div>
</template>
<script setup>
import { event } from "@/components/pingxingyun/event.js";
import { default as AlarmList } from "./components/alarmList";
import { default as TaskList } from "./components/task";
const operatetopButton = () => {
  event.emit("addNewWork", { isBus: "1" });
};
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.operateButton {
  top: vh(100);
  left: calc(0.5vw + vw(344));
}
</style>
