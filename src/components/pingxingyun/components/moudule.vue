<template>
  <div class="modelStyle">
    <div class="modelTitle flex spaceb" v-show="title">
      <div class="flex aligns_center">
        <div class="titleBar"></div>
        <div>{{ title }}</div>
      </div>

      <slot name="rightPanel"></slot>
    </div>
    <div class="modelBody">
      <slot name="anotherMode"></slot>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    default: ""
  }
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.modelStyle {
  font-size: $vw14;
  background: rgba(229, 228, 228, 0.15);
  border-radius: 5px;
  padding: vh(16) vw(20);
  .titleBar {
    width: vw(4);
    height: vh(15);
    margin-right: vw(6);
    border-radius: vw(2);
    background-color: var(--color-primary);
  }
  .modelTitle {
    font-size: $vw16;
  }
  .modelBody {
    padding: vh(8) 0 0;
  }
}
</style>
