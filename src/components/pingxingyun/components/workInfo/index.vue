<template>
  <div class="poi_abs z-in2 workLeft">
    <el-card style="margin-bottom: 2vh">
      <div class="everyTitle">人员信息</div>
      <div>
        <div v-for="item in peopleColumns" :label="item.label" :prop="item.prop" :key="item.prop" class="flex aligns_center everyItem">
          <div class="setLabelStyle">{{ item.label }}:</div>
          <div class="setAllInfo">
            <img :src="item.value" alt="" v-if="['identityPic', 'drivePic'].includes(item.prop) && item.value" />
            <span v-else>{{ item.value || "暂无" }}</span>
          </div>
        </div>
      </div>
    </el-card>
    <el-card>
      <div class="everyTitle">车辆信息</div>
      <div>
        <div v-for="item in carColumns" :label="item.label" :prop="item.prop" :key="item.prop" class="flex aligns_center everyItem">
          <div class="setLabelStyle">{{ item.label }}:</div>
          <div class="setAllInfo">
            <img :src="item.value" alt="" v-if="['carPic', 'licensePic'].includes(item.prop) && item.value" /><span v-else>{{
              item.value || "暂无"
            }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script setup>
import { queryCarLocation, queryCarTrak } from "@/api/screen/product.js";
import { getTaskDetails } from "@/api/screen/work";
import { getAllPoints, getRoutePoints } from "@/components/pingxingyun/components/setMapDataMethod.js";
import { event } from "@/components/pingxingyun/event.js";
import { onBeforeUnmount, shallowRef } from "vue";
import { formCarList, formPeopleList } from "./formList";
const props = defineProps({
  carTypes: {
    type: Array,
    default: () => []
  }
});
const formPeopleDate = ref({});
const formCarDate = ref({});
const peopleColumns = shallowRef([]);
const carColumns = shallowRef([]);
let setCarLocation;
let currentTask;
//处理作业类型
const getCarTypeName = val => {
  let name = "";
  props.carTypes.forEach(element => {
    if (element.dictValue == val) {
      name = element.dictLabel;
    }
  });
  return name;
};
//获取车辆的运行轨迹和车辆实时位置，只有在进行中和已结束会查看车辆轨迹，只有有车牌的时候可以看到车辆的实时位置，如果车辆在进行中时会对车俩轮巡
const getWorkCar = async val => {
  try {
    //没有获取车辆绑定的摄像头数据，通过绑定车辆id,设置车辆相同位置来展示摄像头
    const res = await queryCarLocation({ carNo: formCarDate.value.carNumber });
    if (res.code == 200) {
      const data = {
        pointId: "curCar",
        coordinate: [res.data.lng, res.data.lat]
      };
      const dataC = {
        pointId: "camera" + res.data.id,
        coordinate: [res.data.lng, res.data.lat]
      };
      const listc = getAllPoints([dataC], "camera");
      const marker = getAllPoints([data], "carDown");
      event.emit("showCurCar", marker.concat(listc));
    }
    //获取轨迹
    const trak = await queryCarTrak(currentTask);
    if (trak.code == 200) {
      const path = trak.data.reduce((pre, item) => {
        pre.push({
          lng: item.lng,
          lat: item.lat
        });
        return pre;
      }, []);
      event.emit("showCurTrak", { path });
    }
  } catch (error) {
    console.log(error);
  }
};
const doPeopleForm = data => {
  formPeopleDate.value = data.employeeInfo || {};
  peopleColumns.value = [...formPeopleList];
  peopleColumns.value.forEach(item => {
    item.value = formPeopleDate.value[item.prop];
  });
};
const doCarForm = data => {
  formCarDate.value = data.carInfo || {};
  carColumns.value = [...formCarList];
  carColumns.value.forEach(item => {
    item.value = formCarDate.value[item.prop];
    if (item.prop == "carType") {
      item.value = getCarTypeName(formCarDate.value[item.prop]);
    }
    if (item.prop == "carLoad") {
      item.value = formCarDate.value[item.prop] ? formCarDate.value[item.prop] + "吨" : "";
    }
  });
};
//处理绘制线路数据以及告警点和起始点
const doPoints = (data, val) => {
  data.routePlan.forEach((item, index) => {
    item.pointId = `${val + index}`;
    item.coordinate = [item.lng, item.lat];
  });
  //处理起点和终点的点标记
  const list = getRoutePoints([data.routePlan[0], data.routePlan[data.routePlan.length - 1]]);
  //处理告警的点标记
  const alarmData =
    data.alarm?.map(item => {
      return {
        pointId: item.id,
        disposeStatus: item.disposeStatus,
        coordinate: [item.coordinate.lng, item.coordinate.lat]
      };
    }) || [];
  const redAlarmList = getRoutePoints(
    alarmData.filter(item => item.disposeStatus == 0),
    "redWarning"
  );
  const greenAlarmList = getRoutePoints(
    alarmData.filter(item => item.disposeStatus == 1),
    "greenWarning"
  );
  //只有存在车辆以及任务状态为进行中和已结束才会地图显示移动轨迹
  event.emit("showRoute", {
    points: list.concat(redAlarmList).concat(greenAlarmList),
    routePlan: data.routePlan
  });
};
//获取作业内容
const queryNewWork = async val => {
  clearOPerate(); //做清除操作
  const res = await getTaskDetails(val.taskId);
  currentTask = val.taskId;
  doPeopleForm(res.data);
  doCarForm(res.data);
  doPoints(res.data, val.taskId);
  //只有在任务进行中和已结束同时存在车牌才会显示轨迹和车子位置，进行中还会进行数据轮询
  if (formCarDate.value.carNumber && ["2", "3"].includes(val.status)) {
    getWorkCar();
    if (val.status == "2") {
      setCarLocation = setInterval(getWorkCar, 60000);
    }
  }
};
const clearOPerate = () => {
  if (setCarLocation) {
    clearInterval(setCarLocation);
    setCarLocation = null;
  }
};
onBeforeUnmount(() => {
  clearOPerate();
});
defineExpose({ queryNewWork, clearOPerate });
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.workLeft {
  width: 14vw;
  top: 13vh;
  left: calc(0.5vw + vw(344));
  font-size: $vw14;
  img {
    width: 3vw;
  }
  .everyTitle {
    font-size: $vw16;
    font-weight: 600;
    margin-bottom: 1vh;
  }
  .everyItem {
    margin-bottom: 0.5vh;
    .setLabelStyle {
      width: 4.5vw;
      color: #666;
    }
    .setAllInfo {
      white-space: normal;
      overflow-wrap: break-word;
      flex-grow: 1;
      color: #666;
    }
  }
}
::v-deep .el-card {
  // background-color: rgba(141 92 139 / 50%);
}
</style>
