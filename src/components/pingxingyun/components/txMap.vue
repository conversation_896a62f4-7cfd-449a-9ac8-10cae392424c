<template>
  <div id="TxMapContanter"></div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
let map = null;
let marker = null;
let routeLayer = null;
let infoWindow = null;
let interval = null;
let imageTileLayer = null; //瓦片栅格覆盖
// 在组件初始化时添加全局关闭函数
let currentInfoWindow = null;
const routeColorMap = {
  route: {
    color: "#3777FF",
    width: 8,
    borderWidth: 1,
    borderColor: "#FFF",
    lineCap: "round",
    showArrow: true // 是否显示箭头
  },
  trak: {
    color: "#aa001e", //轨迹颜色
    width: 4, //轨迹宽度
    borderWidth: 1,
    borderColor: "#FFF",
    lineCap: "round", //折线帽样式
    eraseColor: "rgba(190,188,188,1)", //擦除颜色
    showArrow: false // 是否显示箭头
  }
};
window.closeInfoWindow = () => {
  if (currentInfoWindow) {
    currentInfoWindow.close();
    currentInfoWindow = null;
  }
}; // 定义接收的 props
const props = defineProps({ realTimeRoute: { type: Array, default: () => [] } });
watch(
  () => props.realTimeRoute,
  newValue => {
    if (newValue && newValue.length > 0 && map) {
      // 处理实时路线数据
      console.log("接收到实时路线数据:", newValue);
      // 这里可以添加绘制路线的逻辑
    }
  },
  { deep: true }
);
const emits = defineEmits(["marker-click", "map-ready"]);
const loadTMapScript = () => {
  return new Promise((resolve, reject) => {
    if (window.TMap) {
      resolve(window.TMap);
      return;
    }
    const script = document.createElement("script");
    script.type = "text/javascript";
    script.charset = "utf-8";
    script.src = "https://map.qq.com/api/gljs?v=1.exp&key=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77";

    script.onerror = reject;
    script.onload = () => {
      resolve(window.TMap);
    };

    document.head.appendChild(script);
  });
};

onMounted(async () => {
  const res = await loadTMapScript();
  if (res && window.TMap && res === window.TMap) {
    // 加载成功
    console.log("腾讯地图 SDK 加载成功", res);
    initMap();
  } else {
    console.error("腾讯地图 SDK 加载失败");
  }
});

onUnmounted(() => {
  if (map) {
    map.destroy();
    map = null;
  }
  if (interval) {
    clearInterval(interval);
    interval = null;
  }
});
const createRainbowPaths = paths => {
  if (!paths || paths.length < 2) return [];

  const rainbowPaths = [];
  if (paths.length) {
    for (let i = 0; i < paths.length - 1; i++) {
      rainbowPaths.push({
        path: [new window.TMap.LatLng(paths[i].lat, paths[i].lng), new window.TMap.LatLng(paths[i + 1].lat, paths[i + 1].lng)]
      });
    }
  }
  return rainbowPaths;
};
//初始化地图
const initMap = () => {
  map = new window.TMap.Map("TxMapContanter", {
    center: new window.TMap.LatLng(34.00776409604684, 119.1852291724374),
    zoom: 15,

    baseMap: {
      // 设置地图
      type: "vector"
    }
  });
  marker = new window.TMap.MultiMarker({
    id: "marker-layer",
    map: map,
    styles: {
      small: new window.TMap.MarkerStyle({
        // 点标注的相关样式
        width: 20, // 宽度
        height: 20, // 高度
        anchor: { x: 17, y: 23 }, // 标注点图片的锚点位置
        src: "data:image/png;base64,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", // 标注点图片url或base64地址
        color: "#333", // 标注点文本颜色
        size: 16, // 标注点文本文字大小
        direction: "bottom", // 标注点文本文字相对于标注点图片的方位
        offset: { x: 0, y: 8 }, // 标注点文本文字基于direction方位的偏移属性
        strokeColor: "#fff", // 标注点文本描边颜色
        strokeWidth: 2 // 标注点文本描边宽度
      }),
      busNormal1: new window.TMap.MarkerStyle({
        width: 20,
        height: 20,
        anchor: { x: 16, y: 32 },
        color: "#fff",
        fontSize: 10,
        strokeColor: "#999",
        offset: { x: 0, y: 16 },
        src: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAAAXNSR0IArs4c6QAAC7VJREFUeF7tnU1+2zgMxaUs0jlF2mVziqYna3qypKdolzM5xUwW1vyk2I5i64OUCBAPeNnNlKIoAH8+AJTttuEfLUALzFqgpW1oAVpg3gIEhNFBCyxYgIAwPGgBAsIYoAW2WYAKss1uWVf9/vLXw/mCw+Ghbdtv5//uuvd/S5m1bZ/7YV3X/ToPv7l5vv/73+H/86+sBQhIWXs2JxjarvsxTJ0LwN71tO3zCZ77l9fHvdNFv56A7IyAD0Bow5C49q5pfvZDCUyiwUbDCEi+zQaVGBTCKBBrjzQAw7RszUzDvxOQJDM171DUSJsS17hlWA8LlWXecgRkJap+390+DkU1qFokQ3OsXQjLR4sRkIkIQk+hkqGYGUhVeTcMARkFSXQwLnkhKKxBhpggGMuaExmU0ApCMPKSsYighAVkKL6b5u0wj39ZFogESjhAqBpZLMwOjgJJKED+fP705L5dWyb+k2fxDkoIQAbVOByekr3OgXkW6M9Q2vanxxcm3QPCWiMv1veM9qgmbgFhrbEn1Ldf6w0Sl4BQNbYHeKkru5ub7x5SLneAEI5SIb5/Hg9q4goQwrE/qEvPgA6JG0DYwi0d2uXmQ4bEBSCEo1wwS82ECgk0IOxUSYWzzLyIkMACwsM/mSAWn7Vtn7/+89938fsUugEsIEyrCkVAhWmQlAQSEMJRIaoL3xIFEjhACEfhSK04HQIkUIDwnKNiNAvd2jokMIAQDqEINTCt5ddSIAAhHAaiWHgJViExDwjbucKRaWV6o+1f84CwKLcSwfLrsFiPmAaEqZV8UFq7gzVIzAJCOKyFrt56LNUjJgFh3aEXjCbvZKgesQkIv7PKZNxqLspKqmUOEKZWmmFo+14WUi1zgPy5u+1su42rU7OAgVTLFCBUD7XQg7lRbRUxAwjhgIlZ3YVWVhEzgPBAUDfukO5Ws2A3AQjVAylc66z168trlVitctNLE7MwrxN0SHetpSLVAaF6HMO0bZ8XA9b7j4gm0FpDRQhIgmOKDzn+omz/W+X93Dlf0dm/ZdAcDg8hfnn3wvA1VKQqIKHUQ+gnAnob9nEU5deytFWEgBSXh48T9rterxQ5KrF1SRE2HG0VqQaId2dqO3IMlXfbaqoIAdm6Xc9dJ5RKbVmmV1A0T9erAeKxtavpuFRgXH50QPF0vQog7nY2RYelgnE5zpvNtdIsArI14o7X1aw1cpfuCRItta4CiJf0CgmOE0xuIFFSbXVAvDgIEQ5vkGioiDogHtQDGQ5PkGj4gYBkJvIaTslc0ubh8GqukGapAkKHbI5lsQvRP4cj3c0iIBmhJ+2MjKUUHYoMiXQdogoIcv3hKbW6OiP58tdDezg8FaVOazLhNEsNEOQTXc9wnOIYVkXcAAL8ZXDSMq612S7dB3oDu7n5LvW2tJ6CgAISQT3QW7+SG5gaIKj1h9fCfE5NEP0kuYkRkIW8Q9LwFtKqqTVA1iKCdYgKIKj5bURAIH0FDwho/REtvULuaEn5SkdBAAGJqB7nYh3wXESqUCcgM8WAlMGt1h7jdSGmWVL+UgEEsfCTkmwEQPo1ovlMSvF1AEH7zQ/Boo+AyFiAgMjYdXJWKWMrPsLuW8G9eS20qVFBJkKJgDQNXB2CCgicoZumkSr4dm/rihPA+Y2A6EUHAXmzNdRrJwRED5DoHazzgSFSc4WAEBA9CwAqSNM0EhubeJEO1w0RMrR2cJe4H1SKJeQ3AjIRSRI7UYmA1Z6DgAy/uyL7B9cNYRdrCAhEv0lsbARk6hxE8COcsttRudkJyJstCQgBmaSKtSMBmd1ueZLeNHCAsM1bLn1YnUnI2Kv3NTSAgFBBFsNRouAzFP+rS0HrYDVCm5p4DdJ7As7YwTtZiAW6VFpMQGb2UimDr27dBgbApVf9htY0P+9fXoffjC/5pwPI509PTdc9lFy4+FxCki2+7gI3gFR8AlLA85lTRH2rFxIQobMrKsgSNAFVBDG96l0o1VRRAQTV6L3ho6kIonrgAwL4PUsnYZEq/jKzPZXhsBuZoNKrKAhqq/cMiVB+qxL1GTdBVQ/JTUwPEMRO1im4BHeojPgVHQqrHoIt3t7gBCQx7DzXIogHg2O3SfpGDRDkHWpwhmMVQfsWxcs9TaqDpaog6LuUV0jQNy7J+kMVEPRC3WNXCx2OoQ0vdIJ+Lj8TU/Aiw9Cl3BMkHuCQPP+oAoiLNOtoOcnCsMhutDCJJz9I1h/qKZYnx2jIuwQonnwgnV6pAzLUIcjnIRMRq+GkUqB4Sas0U121Nu/poTztYJqO2guJNzg06o8qCuKlm3UVsIbPSbyptmZ6q64gHtOsMSyWUq5BrbvuB9yH1RLkUqtJUgUQj2nWpU9rguIZjJOdpbtXVdq84yBCfXM0YXM7D+kh6f9D4rPSU+uIAIZmelWtBulv7LFoXIJHSlHOUAyRA/a5/5zdZjRWSz3qAgL8IaqNfn27rG2fu677tUdZoijFlJ2lNpo5n1apQU6L8dhd2QTPCJrL69u2/faes8VQiCUbaqpHVQUZ0qyoKrKJIl6krR7VAekXQBVh4KdaICQgVJHU8Ig9rgYcJhSEKhI78FOfPjQgVJHUMIk5rhYcZhRkKNjvbh/bpvkRMwT41JY6V+O1VG3zXholwuk6UcizQE31MKUgbPvmBU6U0drnHlfnUNYMzbavNY/UW09t9TCnIFSResFo7c4W4DAJCAt2a6FaZz21U6vTU5sq0seuYKpVJzAt3NWKephVkJOT2NWyEK66a7AEh3lAeICoG5wW7mYltTKfYp0WyANEC2Grswatz5nnPI3ZGoT1SI4b8cdaS61gFORcjzj7wjn8kC73BFbhMF+DjF3AeqRcQFqayTIcUIAM5yP8BKKl2N6/FsNftgeXYrFo3x+P1maw1rGasg9EkX65cHa2rIV6/nosdqzcADKkW/z8SH5UGrnCet0xNhOkgjDdMhLpG5aBBAdckT7lD76ztSFKK12CBocLQPqHICSVIj7jtohwuAGEkGREaoWhqHC4AoRnJBUiP+WWAGcdS48BXaRftX95kJgSsnpjwOFwpSDnzhYh0QNg5U4oZx1hFISQmGGj8QCHSwUhJPUh8QKHa0D6h+Npuz4syB2rKWu5KtKnHpCQ6EHiDQ73CnJOt/jeljglHuEIAwjTLVk+vMIRChBCIgOJZzjCAUJIykLiHY6QgBCSMpBEgCMsIIRkJyQOXiFJtYD7Nu+SIdgCTg2T0bhAcIRWkJPL+VmSDEiCwUFAjrFBSBIgCQgHARnFBSFZhgThK3oSMM8eEroGubQWIZmOH08vH+YSQkBGFuOnEq/DJzIcTLEmthNC8m6U6HAQkBm9JSRNE+UgcC3lYoo1B0ngN4AJx3tQEJCFLSTiQSLh+BgQBGRFYyNBQjiug4GArCWhQT66SzimA4GAJADSD/F+RhL1IHDN/QRkzUIBTtvZzp0PAgKSAYjH9i9Tq+UAICAZgPRDPUFCONadT0DWbXQ1wkNni3CkOZ6ApNnJFyRBX13f4moCssVqx2tQO1vsWKU7nYCk22pyJBok7FjlOZyA5NlrGpK7267ANOJTsO7INzEBybfZdT0C8JskhGObownINrtBFe2EY7uTCch222FAwo7VLg8TkF3mu77YWtHOjtU+BxOQffYz3dlix2q/cwnIfhuaLNpZd5RxLAEpY0dTkBCOck4lIOVsaaJoJxxlHUpAytqzLiTsWBX3JgEpbtJ6nS12rMo7k4CUt2mVzhY7VjKOJCAydlUt2ll3yDmRgMjZVqUeIRyyDiQgsvYVhYRwyDuPgMjb+OoORV5HYcdKxXMERMXM5Ttb7FjpOI6A6Ni5aNHOjpWe0wiInq2LQMK6Q9dhBETX3ruKdsKh7ywCom/zTZAQjjqOIiB17J7X2WLHqpqXCEg10090tma+HYUdq3pOIiD1bJ9UtLNjVddBBKSu/RfrEdYd9Z1DQOr7YBKS/n/ev7w+GlxeqCURkFDu5sPmWoCA5FqM40NZ4H/H4NAjf7bAWQAAAABJRU5ErkJggg=="
      }),
      // 定义起点样式https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-start.png
      start: new TMap.MarkerStyle({
        width: 24,
        height: 28,
        anchor: { x: 12, y: 14 },
        src: "https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-start.png"
      }),
      // 定义终点样式 https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-end.png
      end: new TMap.MarkerStyle({
        width: 24,
        height: 28,
        anchor: { x: 12, y: 14 },
        src: "https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-end.png"
      }),
      // 定义终点样式 https://mapapi.qq.com/web/miniprogram/demoCenter/images/marker-end.png
      routeItem: new TMap.MarkerStyle({
        width: 20,
        height: 20,
        anchor: { x: 10, y: 10 },
        src: "data:image/png;base64,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"
      }),
      homeworkCar: new TMap.MarkerStyle({
        width: 46,
        height: 34,
        anchor: { x: 23, y: 17 },

        src: "data:image/png;base64,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"
      }),
      carDown: new TMap.MarkerStyle({
        width: 46,
        height: 34,
        anchor: { x: 23, y: 17 },
        faceTo: "map",

        src: "data:image/png;base64,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"
      }),
      currentPostion: new TMap.MarkerStyle({
        width: 30,
        height: 30,
        anchor: { x: 15, y: 15 },
        src: "data:image/png;base64,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"
      }),
      camera: new TMap.MarkerStyle({
        width: 18,
        height: 18,
        anchor: { x: 9, y: 35 },
        src: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAQAElEQVR4AeydB5A1RbXHL+8pij6eYhkQIwqYc8AcMaIYUFEEE8FYAmLAjFmMIAIqYETFUAYQc8SAKMZSMFKKKGDAhGKq0v/vYy/ut3v37u0znedsnbM9d6ZP+vecid09/zPxv5QIbC7lNxM/VPw88TvEXxB/Vfxt8Wni08W/FP9OfJ74n+LzxWeLfyj+mvhT4veLjxa/Rnyg+GHim4r/T+yUCAFPkHjA3kCq9hEfJSYJzlL5J/Ep4neLXyDeTXwH8fbiG4mvLd5avJX4MuJLii8ivrj4CuLtxLcQ7yDeWfwY8X7i54vfJf6G+M/iM8WfER8h3ld8L/ElxE4DEfAEsQN4c4k+Ufwe8a/F3xUfLN5DTBJsqTIXXUmG7iJ+nPi14o+K/yImafZXyVlMhVMoAp4giyN2NVV9kpiEOEPl18WvFz9EfDlxjUTSvEqOcRbjUu6NWuYsRixadFoPAU+Q+Qhtoc2PEL9PzP3AoSpJiKuobI24lNtbTnMf9DOVx4n3Euc808lcW5QwQdoCYpm3/6vl+4u5ISYp3qblB4kvJu6J7qtg3iQmxmNU7iL2+xaBsJw8Qf6LxjW0+CIxO8wHVXJDXOulk9yLRv8vTQ8XHysm9sNVcpOvwskTZDK58WQyOUT8LfFzxNcUj5WurMAfL+Ymn4PE3bU8ahpzgvCk6S1qfRLjySo5kqpwWkKAy8xPaJkzyx1VjpLGmCD3UUvz0o13FY/SstN8BLg3+byqcC92K5WjojYTxNZE20rsSPHxYl66qXAKQICneSepPjf226gcBY0lQZ6i1vyieE+x0zAEeDR8olTwTkhF39R7gtxTzfdZ8avFdN1Q4RQBgStKB++EPqLy1uJuqdcEuapajLfcH1N5Z7FTGgR2lFrOJvQz6+09kUKbTHpMEF7qfU7R0U9KhVNiBOhcSU9lEuV+iW1lV99bgrxMCNIthJd+WgwnlzAjcEtJfkjMy1YVfVAvCcLLPp7ZH9BHszQdBS9bT1AEdP9X0Tb1kCB0L2dA0ejf+la0K95bvtAmj1TZNLWcIJsK+cPEDFC6rEqnuhDgqeFb5RLdeDZT2SS1miD0GeI0/oQmUR+X03Tj4WzC8ODmIm8xQQCakXIMQ20J8DH7elsF/2Ex76VUtEOtJQgAf1nwMlZbhVNDCHDWJ0l2bcjnpt6D0BeIF39MaNASxu7rfxHgvvGd+tlMl59WziBPFaj0JlXh1AECdBplBpjqQ2khQbjJe2X1SLqDoQgwA8yzQoVy1689QRi7wWPC3LiksHeOlHImpG8Yg7M20e8QRgZZdKBL4lNqtnyJPGeqIhV1Us0JwpQ1jN2oE7kwr+j+sqVE6FXM4CMme9PPIEIGWXSgC51BCiqtzGR3jO6s0r1aE4THgjzKrRK0QKd4sMBUQYFi61ZHJ7rXrdhABUZ3MtNkda7WmCDMWMiLperAMjr0XKPcImIpdS9iP2adL0kZc3epqIdqSxA6uPEYsNmuCSua9vv6zfy5KpIQurGRRHlmpUzCzfxczFGc2fTa5mpKEG5CmRqzp5eAp64NfbQtaW1Ec3MhRbdRLfpvMcZEi+WppgRhWv/ehm/+PEMT57CRIYwLTTDjI6NBL1xRcqGWBGFKf7qtl8Qihe0bplC6QmcOGytMJv/5WFng/ZeKslRDgtxDEHD2UNEd5Xgyk8NGiYbhcfZdSxhebrN0gtCBDSCW+9TTMmMiUvYCQDc2esJsGgv3IRw4mUFlui57WTpBAOB62aPOa5A336ksptSdyucL9S6wwOVj0QNoyQThO3sPXgCkHqr8W0HwbQ4VUQhd6IyirHIlfIuRce5F3CyVIHwS7OlFIi5nlEfYfKqNKYk4c3KACGFkkEUHuspFkt8yM6XslN/spNh4EJKjl5eBIe3G90buJAGe2vEhzhBGBll0SMXoiPizB13iDLK7oqQfkQonR2BhBDg4ZJ+DIHeCXFpwcPZQ4eQIBCPAJOSLnEGDFa8lkDtBSI7rr+WMr3cE1kGAr3+RJOtUi7c5Z4LQz+YZ8Vx3TSNFgHsRZrbJEn7OBOHskdNeFgDdSHYEmEWeJMliONcOy/fuupv5O0sLuZFZCOymlQzHVpGWciUIX05NG4lrHxsCWTq3rk6Q+DDvIpU+sbRAcIqKAFcld4uqcYayHAniZ48ZwPuqKAgkP4ukThBmQ7xjFChciSOwGgGuTpLOiJI6QfzssbpRfU1cBJKeRVImCJ8LvlVcLFybI7AKAa5Stl+1NtKKlAnCxzQ3ctN/OAKJEEh2FkmVIEzf40+uEu0NrnYVAowZucqqtRFWpEoQZqaI4J6rcAQWQoA5tXZeqGZgJU+QQMC8erUINJMgzKvrN+fV7kfdOnY7RcaYERXxKMUZpMTlVTxE0mv6vUycJmam9veofJ342WK+ukT/Iphl1rGNOtRFBllVdVoDgQeusd682hPEDF2Q4CdVm+9gXF4lc89eVyXf+nioSr609FKVR4tPWGKWWcc26lAXGWTRgS50qrrTMgS4zGJQ3rJVwxZjJwgf2aQhh3nVh/RxCoPZR66kksnxmGjhN1oeSuhAFzrRjQ1sDdXbg/xWCiLqWSR2gkS/BlTALdF35CxTZjLyje79fIvvV1qXitCNDWxhE9v4kMpeC3o5i0TzM3aCcCkQzbmGFP1MvjKJG290D9Xy6eLchE1s4wO+4FNuH2qwd3s5wZcCVAynmAnCEeyWw12qTcNcf87V1heK2SmZAfDvWi5N+IAv+IRv+Fjap5z2N5cxkkTFcIqZIMn75g8PN5qGf0nTIWJ2Qua2YjI3/ayK8Anf8BFf8bkqBxM6w/wHUdTHTJAdonhUv5KT5SI73b4qfyKunfARX/EZ32v3N4Z/VSbIGO4/eFrEgeCbMVoxsw58xndiyGw6uzleVm8aw2qsMwgZyzP6GD7VquMoOcbTovNUtkr4TgzE0moMi/h9UVWKMpAqVoL03nP3AAHO+BYVEamcKmIhpnIepLcc5UY9VoJsmz7eYhbo43NQMevpDBMTsaWzUFZzVWeQXhNkE7Xxl8WpietlxjPcXIZgllmnn0mJ2IgxqZFCyq8Vw26sM8h2MZypTEcUgGfExCfF6B7Ctz5O1XbeU/Du4gwtf32JWWYd26hDXWSQVZXolCrW6I4GKASrwS8MYyQInecuFeB4C1XpkfyjiI7yEpUJLHiCRPcQ+lLRLec6srGFeC1iG3Woiwyy6EAXOteSC11PrMQcKld7/cEH7hgJMtiJylDeX/58RByD6Lh5mBTxLuJwlTF2QnSgC53oxoZUb0yGX8RM7AbRakUGnxljJEhP9x8cpfnU2dAWv7oUvEL8NXHKj76gGxvYwqbMDSJiB4NBSioSHnzwjpEgg52oBFCu83njPNQdvjvITvs0KbqkODVhA1vYxPZQe2AAFkP11CDvZ5BIrfAP6aEH7N9UWomJA7hMof9T1q8gLTmMTWzjA74srQ4uwAAswCRYuDKBKhKEQTuV4RLsDkNb6YoRLLgkQE8C7gl2XPpdssAHfMEnqx9gASZW+VrkBl/dxLjEav0J1plqTXq7qjARw195n3AFk3QaIXzBJ3yzWgATsFkp39LvS8hZup2osJEnyGQyZEfYXbAfIa6V8A0fLf6RHGBjka1J5uJDnBl7ggy5lOCN99uHgJ9JFh/x1WKOyywwssjWIsMn28y+DE2Qi8gyT1FUNEkcIS03o1dTtCeKWyF8xedQf8EGjELlaqpfNEFavv/4uFqRo6uKIOK6lu7imwVJla2Mr/iM76GegBFYhcrVUr/oJVbLCcKEbJZGPFhCDDxS0RThM75bnLZiFWgrSXU/gxhhZbbCUFEenTKWIlSulvr4Tgyh/liwCrWRqr4niAHZL0rGMi3OEyXXOlliACswazH2ognSImD4/AX+BTKztuwaKFNjdWIgllDfLJiF2khRv+g9SIqAcuj8qMEIHQMNYlWKWGKxYFZD8H8d4sTQx7xDbJeSZUzFSYHGb6r6fJdbRRdELMQUEgyYgV2ITA11L0gQoydjTBDLrOh3MeJbs5glJgt2pTHwBAlsAXq7BopM7hoq0EB9S0wW7EpDcf4QB8Z4BvlxIGBMnmA52gaayV6dmIgtxHAodiG6U9UtegZhYoFUgaXSe06gYp74hO5IgSaKVCcmYgsxHopdiO5UdYsmyC9SRZVQb2gjW16sJXQ/qurQ2EKxi+qsQdkfJfNPsZkWucSap5wu0fO217bN0sDM2lJbHLH8scRmwTCWv6F6Bh/AhyYIDreUJJbGtexE4NICW2KzYFgKC+YXG2Q7RoIMztJBEYQJnx1WfUNtRudtWOjwnyU2C4aloBu8b8ZIED+DlGr+4XZ7P4N4ggTuI5bLA8tOFOhWseqW2CwYlgqwikusH9qjb0Ly3014aXOy59hApIozyLfwpBHe0uBnS0fM0PAssVkwDPUrVv3B+2aMe5DBTsRCYwE9lsa17EQLuFJFFUtsFgxLBHu6jPIeRIWdYiQIL2K+Z3chq6SlcS07UdagBhizxGbBcICLZtEoB+4YCUIEUZxBUWK2NK5lJ0ocRjT1ltgsGEZzOEBRlH2y3wSZjeRltZqpilQsTJadaGHlhSuGxgZ2YFjY7YXMe4IsBNPqSqFHwONXq+hmTWhsodiVBKqqBGHWi7+URCPAdmgjf0O6WxxJJ7fnEjER29xKKzaGYrdCPNtPuuWfFcNarEssfGllzPKNcTaQWxwotF6Ilpgs2K3nR4rtJ8RSGjNBPhHLqcR67mHQ3+qUN/NCtcRkwW6eD6m2RRsaHDNBvpQq2sh672nQt9HOZJCvUcQSkwW73LHz2iHavhgzQehyckpuNAz2+PpS6Hjsn8vO4eJeiFiIKSQeMAO7EJkSdT8mo38WR6GYCYJD0TIXZQmZ8dih6g+TQCsPIuTqmkQMxLJmhTU2WDBbQ1XS1VH3wdgJYrnxS4rWGsrvvsb6eatP1UaOvCqaJmIgltAgLJiF2ohRn089xNCzQUfsBPmMtPK1VRVVEx+Uub7BQ3au3xjkahHBd2II9QeswCxULnf9k2UQVhGHYicIXn2Ifw0w19ShbjKJs2UHW9RO6nr4TgyhdixYhdqIUT/6d0xSJMgHY0SaQceesmH5OtaBknu/uDXCZ3wP9RuMwCpUrkR9btCj2k2RID+Qhy0kCZcN+8hXCz1YQqeJWyF8xWeLv2AEVhbZnDJcWsFRbaZIEBxs5TKLxt8ahw18B4NMKRGrr2ADRqX8DrEb/eyB8VQJwie7eC+CjZqZMdnWHeC3CuwW4toJH/HV4ifYgJFFNrdMkieoqRKEKUnfmRshoz12gu2NsrwYZeqcwWOfjfbnieETvuHjZDKZV3XmNjABm5kbK1v5VvkT2vFSIutTqgTBMgnCSymWa+chO8KvFdxVxYeIayF8wSd8s/o0BBOrTavc0VbB9eRSJghjgkmS9XyoYfvD5MTO4iG0r4T3E5cmfMCXIX6ABZgM0ZFLxtwjEAAACMxJREFUlsv5qG/PlzueMkGwcwz/GuEj5eftxUPoYAnvJI7+PF461yNsYhsf1qs7bzsYgMW8OjVtS3b2IMjUCUKP0dBRa/hVgreQUXaMzVQOIeK9lxRwBCZ+LSYlbGALm9geYozYwQAshujJJXucDH1KnIxSJwiOH8G/Rvha8jPWuJZjpYvHq3uoTNH9Bp3oxga2ZGYwETsYhCkqV/sNqU3nSBCeT783dSAR9XOJwVvnWCrfLEU8EbqtyoPE3xdbCVl0oAud6LbqWilHzMS+cn2tv7m/Zd9K6l+OBCGAls4i+MtN6lNZiMhfka4DxLyVvpHKF4g5AtLrgG0/1e/zlphl1rGNOnQRQQZZdLBNVaMRsRJzNIUZFB2awcYkV4IwqcPbcgQU0cYrpWsHcQr6rpSy0z9e5QPFnBG2Ubn5ErPMOrZRh2RCRpujEzESa3TFCRWSHNG7lczyN1eCYLu1swg+cwO4NwudMrERY0vhnStnXyfOQjkThIwf+ggyCygrjLxRv18u7o2IidiqjmuGcyTHT2asT7IqZ4IQAJcK32ahMX6G/OVBw6VUtk7EQCzE1FosXKqzD2XzO3eC/EGRZQ1Q9mIR3cUZMUnnv1g6c+vBd2Iglty2Y9jLvu/kThBAoit8i5da+H4z/eNdwV4qWyN8xndiaM13/CU5OIOwnI1LJAjBEWyLl1r4zlvmN2mByQEeorJ2wkd8xWd8r93fWf6RGDz1m7Ut6bpSCdLypda0QXipRkc5xiHUOKEaPuEbPuLr1O8WSw6osf1eSF+pBME5LrWKBY4DkXhH6eGNLm92b63l0oQP+IJP+Fban6H2nyYFnEFU5KeSCUK0nDZ5W8xy67yrAuANNzvnI7TM0yIVWQhb2MQ2PuBLFsOJjbxD+l8lLkalE4TAOULQtYLlHpidk14DZyoY+jc9UuXFxLEJnejGBrawie3Ydkrpo+cA+0Yp+xvs1pAgJEdxIDagEfcf89jSv4nhoMwV+wGp54b5hioZCqsiiJBBFh3oQie6sYGtIGUNVGafCP0CVvSwakgQguIyq4f7EWKZxRfVygeIuWH+jsqzxXyjnJLfPH59u9bRJwpmmXVso87yuuhAFzol0iWRHNE+YTAEIVuCDLG4tiz3I0lHh61tutiW6VmBeW93lxf0qoVZZh1nDOpo02iIfaDofcdypGtKEPxiBj+OnCw7jw8BxpazD1QTeW0JAjA8v2dgEMvO40GAGVjuXFu4NSYIGDEw6E8sOI8GAUZI/qu2aGtNEHDi2T6lc/8I8KbfMut8cmSqS5AVEW+y4rf/7A8BLqu496gystoTBNA8SUChTyY5inUjWQTSFhKEODxJQKEvrj45gLuVBMFXTxJQ6IObSA6gbilB8JckqfZ6FQed10VgW9Wo+rJK/l1IrSUIjvPEgwkHWA5hr1sWAbrf0z0m24QLMcJtMUGI+5n6N3QGc6lwyoTAi2Tn3uLq3nPIp7nUaoIQFN/AYAwEvVr57VwnAo+TW88TN0ktJwiAM6CGmQGZqYPfzvUgcJJc4fPRLc69JdcvoNYThCiY5ZwkeRk/nKtA4LXyguT4rMqmqYcEmTbAs7TAXLZ8hlqLOcltLSHwI5UM6HqKyvPFzVNPCUJjMPCKswkj7fjtnA8BMOes8b58JtNb6i1BQOyX+vfoJa6yA5x864nAeIo3Y+N7ii3b5w9KgMYRjTe2lCXsj8Em2HaNcY9nkOU75vKjG8vLt/myHQGwnJ41WLZrqlyy9wSZws+R7jb68Rwxn6dW0QjV5SbYgSFYgmld3iXwZiwJAnRn6d9LxEzevL9KH9YrEBYksAIzsANDsFxQtO1qY0qQaUsxL/Br9IPGfoLKU8ROsxEAGzACKzADu9k1O107xgSZNuXftcBn4fhmBt/LYK5grXISAmABJmADRmCl1eOjMSfI8tZm+k4mY+Pa+jBtGN2RcilmYgcDsAATrR43eYJs3P70H3qSVvHJZbrUd33kVJxTYqI2YiZ2MJiuH33pCTJ7FzhDq+lSz9H0GC33SkzSx3sMpvok5l7jNMflCTIfum9qM9OA7qKyN3quAmKSvmZG98nf7OQJshjkfBV278WqNlHryfLyxWKndRDwBFkHoGWbj9TyAeLWaTcFcKjYaQEEPEEWAGlZlYO0/HGxhbiU4ZKGx6d7SMF+4ueLXy0+SsxZinsCmGXWsY061EUGWXSgSyLB9C5J8BUqFUmpG+WeIOFNyZecwqUukGDn5/Hpm/XzYPELxXzuYC+V3Oew88Mss45t1KEuMsiiQ9VNRIKYBMcq5AkS3vLHSuSr4taI6ZJOaM3p0v56gthagE+g2STLSR1XznS7lj1BbG33PZtYUakWfS4KGMY9QUAhnE8OFyku0aLPM0DLu8oTxIb3uRJraYZAfMVnue0UgoAnSAhaG9dtafx1S75ujHLhX54ghRvAzdeNgCdI3e3j3hVGwBOkcAO4+YoQmOGKJ8gMUHyVIzBFwBNkioSXjsAMBDxBZoDiqxyBKQKeIFMkvHQEZiDgCTIDFF/lCEwRiJUgU31eOgJdIeAJ0lVzejCxEfAEsSN6p0DRrQLrz6seqivU13m2R7VtbAlyebXufcRMwHCgSiv/WLKhtJ0EmCXFanMqhw50SV0Q4fNUh6UEM7C7YpDVxiuPJUH2VDt9WHyO+HgxH5ZkrLeVt5EOC91EQlabUzl0SE0w4fNUh6UEM7D7lSx/TkySbaqya2ogQQbhv5OkPy1mRhKWtegUAQEu2UiyE6XrfuJuqecE4QjHWYPv5nXbgIUD2172megarLXYH/WaIEzZzxGuvxarMyKwBvM6vRvgVY8JwtxRzFI+ABYXNSAA5mBvEK1XpLcE2VpQv17sVAYBsKcNylhPYLW3BNlHGPEoV8UC5FViIwD2tEFsvcX09ZQgvBvoqnGK7RXDDNMGtMUwLZVI95Qg960EU3djMummLXpKEN7y+s5ZBwLdtEVPCXL1OvYN90IIdNMWniBqzRQ0cp3dJMh/AAAA//+DOAOZAAAABklEQVQDADExa1rW63GLAAAAAElFTkSuQmCC"
      }),
      redWarning: new TMap.MarkerStyle({
        width: 24,
        height: 24,
        anchor: { x: 12, y: 12 },
        src: "data:image/png;base64,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"
      }),
      greenWarning: new TMap.MarkerStyle({
        width: 24,
        height: 24,
        anchor: { x: 12, y: 12 },
        src: "data:image/png;base64,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"
      })
    },
    geometries: [
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.002648, 119.18075), // 标注点位置
        content: "西门站 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.003053, 119.190369), // 标注点位置
        content: "白金酒库" // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.006531, 119.192107), // 标注点位置
        content: "二道门 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.008673, 119.193524), // 标注点位置
        content: "三号门 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.00211, 119.183748), // 标注点位置
        content: "包一车间 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.002648, 119.18075), // 标注点位置
        content: "仓储/勾储 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.002635, 119.177598), // 标注点位置
        content: "食堂站 " // 标注点文本
      },
      {
        styleId: "small", // 对应中的样式id
        position: new window.TMap.LatLng(34.012726, 119.193935), // 标注点位置
        content: "包二车间 " // 标注点文本
      }
    ]
  });

  routeLayer = new window.TMap.MultiPolyline({
    id: "route-layer",
    map: map,
    styles: {
      "route-style": new window.TMap.PolylineStyle(routeColorMap["route"]),
      "trak-style": new window.TMap.PolylineStyle(routeColorMap["trak"])
    },
    geometries: [
      // {
      //   id: "route",
      //   styleId: "route-style",
      //   rainbowPaths: createRainbowPaths()
      // }
    ]
  });
  //初始化infoWindow
  infoWindow = new TMap.InfoWindow({
    map: map,
    position: new TMap.LatLng(34.00776409604684, 119.1852291724374),
    offset: { x: 0, y: -32 }, //设置信息窗相对position偏移像素，为了使其显示在Marker的上方
    enableCustom: true,
    content: '<div class="info-window">信息窗内容</div>'
  });
  infoWindow.close(); //初始关闭信息窗关闭
  // 设置鼠标悬停为 pointer
  marker.on("mousemove", function (evt) {
    if (evt.geometry.properties && evt.geometry.properties.showWindow) {
      infoWindow.setPosition(evt.geometry.position); //设置信息窗位置
      let contentL = [];
      for (const key in evt.geometry.properties) {
        if (key !== "showWindow") {
          contentL.push(`${evt.geometry.properties[key] || ""}`);
        }
      }
      infoWindow.setContent(`<div class="info-window">${contentL.join("<br/>")}</div>`); //设置信息窗内容
      infoWindow.open(); //打开信息窗
    }
    map.getContainer().style.cursor = "pointer";
  });
  marker.on("mouseout", function (evt) {
    infoWindow.close();
    map.getContainer().style.cursor = "";
  });
  marker.on("moving", function (evt) {
    let passedLatLngs = e.car && e.car.passedLatLngs;
    if (passedLatLngs) {
      routeLayer.eraseTo("trak", passedLatLngs.length - 1, passedLatLngs[passedLatLngs.length - 1]);
    }
  });
  marker.on("click", evt => {
    const { lat, lng } = evt.geometry.position;
    const name = evt.geometry.content || evt.geometry.properties?.title || "";
    const id = evt.geometry.id || "";

    // 只传递可序列化的简单对象
    emits("marker-click", { lat, lng, name, id, geometry: { ...evt.geometry, position: { lat, lng } } });
  });
  // 地图点击添加 marker
  map.on("click", evt => {
    emits("map-click", evt.latLng);
  });
  // 监听地图加载完成事件
  map.on("complete", function () {
    console.log("地图加载完成");
    // 地图完全加载后再触发 map-ready 事件
    emits("map-ready");
  });
  // 使用 setTimeout 确保地图加载完成
  setTimeout(() => {
    console.log("地图加载完成");
    emits("map-ready");
  }, 500);
};
//销毁瓦片
const destroyImageTileLayer = () => {
  imageTileLayer && imageTileLayer.destroy();
  imageTileLayer = NULL;
};
//创建瓦片
const creteImageTileLayer = () => {
  if (imageTileLayer) return;
  imageTileLayer = new TMap.ImageTileLayer({
    getTileUrl: function (x, y, z) {
      //拼接瓦片URL
      var url = "https://base.oss.yongtoc.com/CommonModel/jsy/hub/" + z + "/z=" + z + "&y=" + y + "&x=" + x + ".jpg";
      return url;
    },
    tileSize: 256, //瓦片像素尺寸
    minZoom: 14, //显示自定义瓦片的最小级别
    maxZoom: 80, //显示自定义瓦片的最大级别
    visible: true, //是否可见
    zIndex: 10, //层级高度（z轴）
    opacity: 0.8, //图层透明度：1不透明，0为全透明
    map: map //设置图层显示到哪个地图实例中
  });
};
//地图类型切换
const changeMapType = mapType => {
  if (!map) return;

  map.setBaseMap({
    type: mapType // 选择地图类型，如 'vector' 或 'satellite'
  });
  if (mapType == "satellite") creteImageTileLayer();
  else destroyImageTileLayer();
};
//轨迹移动
let curCar;
const movAlong = val => {
  const { path } = val;

  marker.moveAlong(
    {
      curCar: {
        path: createRainbowPaths(path),
        speed: 60
      }
    },
    {
      autoRotation: true
    }
  );
};
// const addMarker = (latlng, options = {}) => {
//   console.log("addMarker", latlng, options, map);
//   if (!map || !window.TMap) return;
//   const markerConfig = {};
//   if (marker) {
//     const oldGeometries = marker.getGeometries() || [];
//     marker.setGeometries([...oldGeometries, markerConfig]);
//   }
// };
// 暴露批量设置marker的方法,调用此方法需先清除旧有的点标志数据
const setMarkers = markersData => {
  if (marker) {
    const oldGeometries = marker.getGeometries() || [];
    // 合并新旧 marker
    const allMarkers = [...oldGeometries, ...markersData];
    marker.setGeometries(allMarkers);
  }
};
// 添加一个设置路线的方法
const setRoute = (routePoints, id = "route") => {
  if (!map || !window.TMap) return;
  if (routeLayer) {
    // 先获取当前已有的路线

    routeLayer.updateGeometries([{ id, styleId: id == "route" ? "route-style" : "trak-style", rainbowPaths: createRainbowPaths(routePoints) }]);
  } else {
    console.error("routeLayer 未初始化");
  }
};

// 移除一个设置路线的方法
const removeRoute = () => {
  if (!map || !window.TMap) return;
  if (routeLayer) {
    routeLayer.remove("route");
    routeLayer.remove("trak");
  } else {
    console.error("routeLayer 未初始化");
  }
};
//更新一个点位
const updateMarkers = val => {
  if (marker) {
    marker.updateGeometries(val);
  }
};
// 重置地图上的markers
const resetMarkers = () => {
  if (marker) {
    const defaultGeometries = marker.getGeometries().slice(0, 8); // 假设默认点是8个
    marker.setGeometries(defaultGeometries);
  }
};
defineExpose({ setMarkers, setRoute, removeRoute, resetMarkers, changeMapType, movAlong, updateMarkers });
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.TxMapContanter {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
::v-deep .info-window {
  background-color: #ffffff;
  border-radius: vw(4);
  border: 1px solid #e4e7ed;
  padding: 15px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  text-align: left !important;
  font-size: vh(16);
}
</style>
