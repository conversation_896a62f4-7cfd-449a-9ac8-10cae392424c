<template>
  <models title="作业类型">
    <template #rightPanel>
      <div>
        <span class="mr5 resetFont">选择日期</span>
        <el-date-picker
          v-model="dateValue"
          type="daterange"
          value-format="YYYY-MM-DD"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
    </template>
    <template #anotherMode>
      <div class="flex spaceb aligns_center">
        <div ref="domRef" :style="{ width: '20vw', height: '12vh' }"></div>
        <div class="flex spaceb">
          <div class="flex aligns_center">
            <img class="image-before" :src="serverImg" alt="" />
            <div>
              <div class="text-center">服务商</div>

              <div ref="serverRef" class="server text-center set-model-fontB set-model-fonttrans">0</div>
            </div>
          </div>

          <div class="flex aligns_center">
            <img class="image-before" :src="workerImg" alt="" />
            <div>
              <div class="text-center">作业人员</div>

              <div ref="workerRef" class="worker text-center set-model-fontB set-model-fonttrans">0</div>
            </div>
          </div>
          <div class="flex aligns_center">
            <img class="image-before" :src="workCarImg" alt="" />
            <div>
              <div class="text-center">作业车辆</div>
              <div ref="workCarRef" class="workCar text-center set-model-fontB set-model-fonttrans">0</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </models>
</template>
<script setup>
import models from "@/components/pingxingyun/components/moudule.vue";
import { userGrow } from "@/components/pingxingyun/components/numberGrow";
import { useChartData } from "@/components/pingxingyun/components/setChart.js";
import { fitChatSizeVh, fitChatSizeVw } from "@/utils/echartResize";

import { requireImg } from "@/utils/ruoyi";
const getFirstdayMonth = () => {
  let today = new Date();
  today.setDate(1);
  return today.toISOString().slice(0, 10);
};
const getCurdayMonth = () => {
  let today = new Date();

  return today.toISOString().slice(0, 10);
};
const { domRef, domChart, initCharts } = useChartData();
const serverRef = ref();
const workerRef = ref();
const workCarRef = ref();
const domRefData = ref({});
const dateValue = ref([]);
const { numberGrow } = userGrow();
const serverImg = requireImg("homePage/server.png");
const workCarImg = requireImg("homePage/workCar.png");
const workerImg = requireImg("homePage/worker.png");
const emits = defineEmits(["changeDateRange"]);
const props = defineProps({
  dispatchKindCount: {
    type: [Array],
    default: () => []
  },
  supplierCount: {
    type: Number,
    default: 0
  },
  employeeCount: {
    type: Number,
    default: 0
  },
  carCount: {
    type: Number,
    default: 0
  }
});
watch(
  () => props.supplierCount,
  (newValue, oldValue) => {
    numberGrow(serverRef?.value, newValue, oldValue);
  }
);
watch(
  () => props.employeeCount,
  (newValue, oldValue) => {
    numberGrow(workerRef?.value, newValue, oldValue);
  }
);
watch(
  () => props.carCount,
  (newValue, oldValue) => {
    numberGrow(workCarRef?.value, newValue, oldValue);
  }
);
/**
 * 监听日期变化
 */
watch(
  () => dateValue.value,
  (newValue, oldValue) => {
    emits("changeDateRange", newValue);
  }
);
/**
 * 监听chart数据变化
 */
watch(
  () => props.dispatchKindCount,
  val => {
    domRefData.value = val;
    const sum = val.reduce((pre, next) => {
      return (pre += next.count);
    }, 0);
    const opt = {
      title: [
        {
          text: [`{a|作业数}`, `{b|${sum}}`].join(`\n`)
        }
      ],
      series: [
        {
          data: val
        }
      ]
    };
    domChart.value.setOption(opt);
  },
  {
    deep: true
  }
);

const option = {
  title: [
    {
      text: "",
      top: "center",
      left: "25%",
      textStyle: {
        rich: {
          a: {
            fontSize: fitChatSizeVh(12),
            lineHeight: fitChatSizeVh(16),
            color: "#333333",
            align: "center"
          },
          b: {
            fontSize: fitChatSizeVh(11),
            lineHeight: fitChatSizeVh(12),
            color: "#333333",
            align: "center"
          }
        }
      }
    }
  ],
  tooltip: {
    trigger: "item",
    formatter: function (params) {
      return `${params.name}:${params.value}`;
    },
    backgroundColor: "rgba(60, 50, 50, 0.8)",
    textStyle: {
      color: "#f2f2f2",
      fontSize: fitChatSizeVw(12)
    },
    position: "inside",
    padding: [5, 5, 5, 5]
  },
  legend: {
    orient: "vertical",
    right: 0,
    top: "middle",
    z: 20,
    icon: "circle",
    itemWidth: 6,
    textStyle: {
      rich: {
        name: {
          color: "#b9b4b5",
          fontSize: fitChatSizeVw(12),
          padding: [0, 0, 0, 5],
          verticalAlign: "middle"
        },
        value: {
          color: "#b9b4b5",
          fontSize: fitChatSizeVw(12),
          padding: [0, 0, 0, 5],
          verticalAlign: "middle"
        },
        percent: {
          color: "#b9b4b5",
          fontSize: fitChatSizeVw(12),
          padding: [0, 0, 0, 5],
          verticalAlign: "middle"
        }
      }
    },
    formatter: function (name) {
      const obj = domRefData.value.find(item => item.name == name);
      const sum = domRefData.value.reduce((pre, next) => {
        return (pre += next.count);
      }, 0);

      return `{name|${name.length > 5 ? `${name.slice(0, 4)}...` : name}}{value|${obj.value}}{percent|${((obj.value * 100) / sum).toFixed(2)}%}`;
    }
  },
  textStyle: {
    color: "#ccc",
    fontSize: fitChatSizeVw(12)
  },

  color: ["#aa001e", "#FEE186", "#2B80FF", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
  grid: {
    left: 0,
    right: 0,
    bottom: fitChatSizeVh(15),
    top: fitChatSizeVh(20),
    containLabel: true
  },
  series: [
    {
      type: "pie",
      radius: ["70%", "80%"],
      center: ["30%", "50%"],
      avoidLabelOverlap: false,
      minAngle: fitChatSizeVw(4),
      padAngle: fitChatSizeVw(2),
      itemStyle: {
        borderRadius: fitChatSizeVw(5)
      },
      label: {
        show: false,
        position: "center"
      },
      data: [] //[20, 30, 40, 50]
    }
  ]
};

const queryWork = async () => {
  try {
    // const res = await getOrderNumTendency({
    //   dateType: curData
    // });
    const res = await Promise.resolve([
      { name: "生产作业", value: 45 },
      { name: "接驳作业", value: 30 },
      { name: "普通作业", value: 11 }
    ]);
    domRefData.value = res;
    //startAutoPlay();
  } catch (err) {
    console.log(err);
  }
};
onMounted(() => {
  nextTick(() => {
    initCharts(option);
  });
  dateValue.value = [getFirstdayMonth(), getCurdayMonth()];
  //emits("changeDateRange", [getFirstdayMonth(), getCurdayMonth()]);
  //queryWork();
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.image-before {
  width: vw(53);
  height: vw(53);
  margin: 0 vw(10);
}
.resetFont {
  font-size: $vw14;
  filter: brightness(0.7);
  color: #e4f3ff;
}
.server::after {
  transform: skewX(10deg);
  font-size: $vw12;
  content: "家";
  font-weight: 400;
  margin-left: vw(4);
}
.worker::after {
  content: "人";
  transform: skewX(10deg);
  font-size: $vw12;
  font-weight: 400;
  margin-left: vw(4);
}
.workCar::after {
  content: "辆";
  transform: skewX(10deg);
  font-size: $vw12;
  font-weight: 400;
  margin-left: vw(4);
}
.set-model-fonttrans {
  transform: skewX(-10deg);
  font-size: vw(24);
  font-weight: 700;
}
</style>
