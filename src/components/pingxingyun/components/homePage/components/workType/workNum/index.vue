<template>
  <models title="作业数量趋势">
    <template #anotherMode>
      <div ref="domRef" :style="{ width: '100%', height: '22vh' }"></div>
    </template>
  </models>
</template>
<script setup>
import models from "@/components/pingxingyun/components/moudule.vue";
import { useChartData } from "@/components/pingxingyun/components/setChart.js";
import { autoScrollHandle } from "@/utils/echartAnimation";
import { fitChatSizeVh, fitChatSizeVw } from "@/utils/echartResize";
import { shallowRef } from "vue";

const { domRef, domChart, initCharts } = useChartData();

const domRefData = ref({});
const yAxis = shallowRef([]);
const props = defineProps({
  taskStatusTrends: {
    type: Object,
    default: () => []
  }
});
watch(
  () => props.taskStatusTrends,
  val => {
    const opt = {
      yAxis: { data: val.date || [] },

      series: [
        {
          data: val.notStartedCount || []
        },
        {
          data: val.progressCount || []
        },
        {
          data: val.finishCount || []
        }
      ]
    };
    domChart.value.setOption(opt);
    autoScrollHandle(opt, domChart.value, 3000, 10, "Y");
  },
  {
    deep: true
  }
);
const option = {
  tooltip: {
    trigger: "axis",
    // formatter: function (params) {
    //   return `${params.name}:共${params.value}个`;
    // },
    backgroundColor: "rgba(60, 50, 50, 0.8)",
    textStyle: {
      color: "#f2f2f2"
      // fontSize: fitChatSizeVw(12)
    },
    position: "inside",
    padding: [5, 5, 5, 5]
  },
  xAxis: {
    type: "value",
    axisLabel: {
      fontSize: fitChatSizeVw(12)
    }
  },
  yAxis: {
    type: "category",
    axisTick: { show: false },
    boundaryGap: true,
    axisLine: {
      show: true,
      lineStyle: {
        color: "rgba(225, 222, 222,1)"
      }
    },
    axisLabel: {
      color: "#333",
      fontSize: fitChatSizeVw(12)
    },
    data: []
  },
  textStyle: {
    color: "#ccc",
    fontSize: fitChatSizeVw(12)
  },

  color: ["#aa001e", "#999", "#ccc", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
  grid: {
    left: "10%",
    right: "10%",
    bottom: fitChatSizeVh(4),
    top: fitChatSizeVh(4),
    containLabel: true
  },
  series: [
    {
      name: "未开始",
      type: "bar",
      stack: "total",
      data: [] //
    },
    {
      name: "进行中",
      type: "bar",
      stack: "total",
      data: [] //
    },
    {
      name: "已完成",
      type: "bar",
      stack: "total",
      data: [] //
    }
  ],
  dataZoom: [
    {
      yAxisIndex: 0, // 这里是从X轴的0刻度开始
      show: false, // 是否显示滑动条，不影响使用
      type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
      startValue: 0, // 从头开始。
      endValue: 10 // 一次性展示多少个。
    }
  ]
};

const queryWork = async date => {
  try {
    // const res = await getOrderNumTendency({
    //   dateType: curData
    // });
    const res = await Promise.resolve([
      { noValue: 45, pengding: 33, full: 11, date: "5-1" },
      { noValue: 5, pengding: 23, full: 11, date: "5-2" },
      { noValue: 13, pengding: 33, full: 18, date: "5-3" },
      { noValue: 45, pengding: 33, full: 11, date: "5-4" },
      { noValue: 5, pengding: 23, full: 11, date: "5-5" },
      { noValue: 13, pengding: 33, full: 18, date: "5-6" },
      { noValue: 45, pengding: 33, full: 11, date: "5-7" },
      { noValue: 5, pengding: 23, full: 11, date: "5-8" },
      { noValue: 13, pengding: 33, full: 18, date: "5-9" },
      { noValue: 45, pengding: 33, full: 0, date: "5-10" },
      { noValue: 5, pengding: 23, full: 11, date: "5-11" },
      { noValue: 13, pengding: 33, full: 18, date: "5-12" }
    ]);
    const noValues = [];
    const pengdings = [];
    const fulls = [];
    yAxis.value = [];
    res.forEach(item => {
      yAxis.value.push(item.date);
      noValues.push(item.noValue);
      pengdings.push(item.pengding);
      fulls.push(item.full);
    });
    domRefData.value = [noValues, pengdings, fulls];

    //startAutoPlay();
  } catch (err) {
    console.log(err);
  }
};
defineExpose({
  queryWork
});
onMounted(() => {
  nextTick(() => {
    initCharts(option);
  });

  //queryWork();
});
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.image-before {
  width: vw(56);
  height: vw(40);
  margin: 0 vw(10);
}
.server::after {
  content: "家";
}
.worker::after {
  content: "人";
}
.workCar::after {
  content: "辆";
}
.set-model-fonttrans {
  transform: skewX(-10deg);
  font-size: $vw16;
}
</style>
