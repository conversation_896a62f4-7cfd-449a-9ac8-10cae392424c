<template>
  <div class="typeContainer">
    <el-card shadow="hover">
      <Type
        @changeDateRange="changeDateRange"
        :dispatchKindCount="workData.dispatchKindCount"
        :supplierCount="workData.supplierCount"
        :employeeCount="workData.employeeCount"
        :carCount="workData.carCount"
      ></Type>
      <Number ref="workNumRef" style="margin-top: 1vh" :taskStatusTrends="workData.taskStatusTrends"></Number>
    </el-card>
  </div>
</template>
<script setup>
import { queryWork } from "@/api/screen/homePage";
import { reactive } from "vue";
import Type from "./type";
import Number from "./workNum";
const workNumRef = ref(null);
const workData = reactive({ dispatchKindCount: [], supplierCount: 0, employeeCount: 0, carCount: 0, taskStatusTrends: {} });
const getWorkData = async val => {
  const [startDate, endDate] = val;
  console.log(startDate, endDate);
  const res = await queryWork({ startDate, endDate });
  if (res?.code == 200) {
    workData.dispatchKindCount = res.data?.dispatchKindCount || [];
    workData.dispatchKindCount.forEach(item => {
      item.value = item.count || 0;
      item.name = item.kindName || "";
    });
    workData.supplierCount = res.data?.supplierCount || 0;
    workData.employeeCount = res.data?.employeeCount || 0;
    workData.carCount = res.data?.carCount || 0;
    const data = res.data?.taskStatusTrends || [];

    workData.taskStatusTrends.date = [];
    workData.taskStatusTrends.progressCount = [];
    workData.taskStatusTrends.finishCount = [];
    workData.taskStatusTrends.notStartedCount = [];
    data.forEach(item => {
      const date = item.date.replace(/\//g, "-");
      workData.taskStatusTrends.date.push(date);
      workData.taskStatusTrends.progressCount.push(item.progressCount || 0);
      workData.taskStatusTrends.finishCount.push(item.finishCount || 0);
      workData.taskStatusTrends.notStartedCount.push(item.notStartedCount || 0);
    });
  }
};
const changeDateRange = val => {
  //workNumRef.value?.queryWork(val);
  getWorkData(val);
};
</script>
<style lang="scss" scoped>
.typeContainer {
  height: 100%;

  width: 49.5%;
  .el-card {
    height: 100%;
  }
}
</style>
