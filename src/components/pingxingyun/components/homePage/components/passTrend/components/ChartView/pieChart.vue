<template>
    <div ref="pieRef" class="pie-chart" v-loading="loading"></div>
</template>

<script setup>
import { fitChatSizeVh, fitChatSizeVw } from "@/utils/echartResize";
import * as echarts from "echarts";
import { debounce } from 'lodash-es';
import { onMounted, ref } from "vue";
// 添加loading状态
const loading = ref(false);
const props = defineProps({
  info: {
    type: Object,
    default: () => {}
  }
});
const pieRef = ref(null); 
const pieInstance = ref(null); // 饼图实例
const pieColor = ["#A9E28DFF", "#FAAE13", "#AA001E", "#999999FF"];


const upDate = (list) => {
  if (!pieInstance.value) return;
  pieInstance.value.setOption({
    legend: [{
      formatter: param => `${param} ${props.info[param]}人`
    }],
    series: [
      {
        data: list
      }
    ]
  });
}

// 初始化饼图
const initPieChart = () => {
  if (!pieRef.value) return;
  // 销毁旧实例（避免重复初始化）
  if (pieInstance.value) pieInstance.value.dispose();
  pieInstance.value = echarts.init(pieRef.value);
  const option = {
    color: pieColor,
    tooltip: {
      show: false,
      trigger: "item",
      formatter: "{b}: {c} ({d}%)"
    },
    legend: [
      {
        icon: "circle",
        left: "48%",
        top: "center",
        align: "left",
        orient: "vertical",
        itemWidth: fitChatSizeVw(7),
        itemHeight: fitChatSizeVh(7),
        itemGap: fitChatSizeVw(5),
        textStyle: {
          fontSize: fitChatSizeVw(12),
          color: "#999999"
        },
        tooltip: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '',
        type: "pie",
        minAngle: fitChatSizeVw(4),
        padAngle: fitChatSizeVw(2),
        itemStyle: {
          borderRadius: fitChatSizeVw(5)
        },
        center: ["25%", "50%"],
        radius: ["70%", "80%"],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: []
      }
    ]
  };
  pieInstance.value.setOption(option);
};
// Handle window resize
const handleResize = debounce(() => {
  pieInstance.value?.resize();
}, 300);

// 组件挂载后初始化图表
onMounted(() => {
  initPieChart();
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  pieInstance.value?.dispose(); // 销毁实例
});
defineExpose({ upDate, loading });
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.pie-chart {
  height: 10vh;
}
</style>
