<template>
  <div class="data-cards">
    <div v-for="(item, index) in cardList" :key="index" class="data-card" :style="{
      backgroundImage: `url(${item.icon})`
    }">
      <span class="card-title">{{ item.title }}</span>
      <div class="stat-value-container">
        <span class="card-value">{{ item.value }}</span>
        <span class="stat-unit" >人/次</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import carnumber from "@/assets/images/carnumber.png";
import passersnumber from "@/assets/images/passersnumber.png";
import peoplenumber from "@/assets/images/peoplenumber.png";
import visitornumber from "@/assets/images/visitornumber.png";
import { computed } from "vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      passengerCount: 0,
      internalCount: 0,
      vehicleCount: 0,
      appointmentCount: 0
    })
  }
});

const cardList = computed(() => [
  {
    title: "通行人数",
    value: props.data.passengerCount,
    icon: passersnumber
  },
  {
    title: "内部人数",
    value: props.data.internalCount,
    icon: peoplenumber
  },
  {
    title: "内部车辆",
    value: props.data.vehicleCount,
    icon: carnumber
  },
  {
    title: "预约访客",
    value: props.data.appointmentCount,
    icon: visitornumber
  }
]);
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.data-cards {
  display: flex;
  justify-content: space-around;
  margin-bottom: vh(10);

  .data-card {
    padding: vh(14) vw(18);
    background-repeat: no-repeat;
    background-size: vw(203) vh(89);
    width: vw(203);
    height: vh(89);
    display: flex;
    flex-direction: column;
    justify-content: space-around;

    .card-title {
      display: inline-block;
      margin-left: vw(12);
      font-size: $vw16;
      color: #111111;
      line-height: vh(22);
    }
    .stat-value-container {
      display: flex;
      align-items: baseline;
    }
    .card-value {
      display: inline-block;
      margin-left: vw(12);
      font-size: vw(21);
      color: #111111;
      line-height: vh(22);
      font-weight: 800;
    }
    .stat-unit {
      font-size: $vw12;
      color: #111111;
      margin-left: vw(2);
    }
  }
}
</style>