<template>
  <div class="passContainer">
    <el-card class="pass-card" shadow="hover">
      <!-- 数据卡片 -->
      <data-cards :data="statisticsData" icon-color="#f56c6c" />

      <!-- 趋势图 -->
      <div class="trend-card">
        <el-row class="top-height">
          <el-col :span="8">
            <!-- <el-image style="width: 4px; height: 15px" :src="sectionurl" fit="fill" /> -->
            <div class="title-wrapper">
              <div class="title-bar"></div>
              <h3 class="section-title">通行趋势</h3>
            </div>
          </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="12">
            <el-form ref="formRef" :model="formData" label-width="100px">
              <el-form-item label="选择日期" prop="dateRange">
                <el-date-picker
                  v-model="formData.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  :disabledDate="disabledDate"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div ref="trendChartRef" class="trend-chart" v-loading="loading"></div>
      </div>

      <!-- 饼图区域 -->
      <div class="pie-charts">
        <div class="pie-card">
          <div class="card-header">访客预状态</div>
          <!-- <div ref="pieChart1Ref" class="pie-chart"></div> -->
           <PieChart ref="appionPieChartRef" :info="infoMap1"></PieChart>
        </div>
        <div class="pie-card">
          <div class="card-header">访客入园状态占比</div>
          <!-- <div ref="pieChart2Ref" class="pie-chart"></div> -->
          <PieChart ref="arriveChartRef" :info="infoMap2"></PieChart>
        </div>
        <div class="pie-card">
          <div class="card-header">预约对象类型</div>
          <!-- <div ref="pieChart3Ref" class="pie-chart"></div> -->
          <PieChart ref="objChartRef" :info="infoMap3"></PieChart>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { transitCount, transitTrends } from "@/api/screen/homePage.js";
import { fitChatSizeVw } from "@/utils/echartResize";
import * as echarts from "echarts";
import { debounce } from 'lodash-es';
import { onMounted, ref } from "vue";
import PieChart from "./components/ChartView/pieChart.vue";
import DataCards from "./components/DataCards/index.vue";
// 添加loading状态
const loading = ref(false);
const getFirstdayMonth = () => {
  let today = new Date();
  today.setDate(1);
  return today.toISOString().slice(0, 10);
};
const getCurdayMonth = () => {
  let today = new Date();
  return today.toISOString().slice(0, 10);
};
// 保存 ECharts 实例（新增）
const appionPieChartRef = ref(null);
const arriveChartRef = ref(null);
const objChartRef = ref(null);
const trendChartInstance = shallowRef(null); // 趋势图实例
const appointmentStatusT = ref([]);
const arriveStatusT = ref([]);
const objCountT = ref([]);
const trendData = ref([]);
const appointmentStatusMap = {
  '-3': '已失效',
  '-2': '已取消',
  '-1': '审核拒绝',   
  '0': '未审核',
  '1': '审核中',
  '2': '审核通过' 
};
const arriveStatusMap = {
  0: '未入园',
  1: '已入园',
  2: '已出园' 
};

// 统计数据
const statisticsData = ref({
  passengerCount: 0,
  internalCount: 0,
  vehicleCount: 0,
  appointmentCount: 0
});

// 日期范围
const formRef = ref(null);
const formData = ref({
  dateRange: [getFirstdayMonth(), getCurdayMonth()]
});

// 图表引用
const trendChartRef = ref(null);
const infoMap1 = ref({});
const infoMap2 = ref({});
const infoMap3 = ref({});

// 禁用今年以外的日期
const disabledDate = time => {
  const currentYear = new Date().getFullYear();
  const year = new Date(time).getFullYear();
  return year !== currentYear;
};

// 转换数据方法
const transformPieData = data => {
  const newmap = data.map(item => {
    const { name, value } = item;
    return { [name]: value };
  });
  return newmap;
};

// 计算属性获取转换后的map
const getInfoMap = (data) => {
  const mapArray = transformPieData(data);
  // 将数组转换为单个对象
  return mapArray.reduce((acc, curr) => {
    return {...acc,...curr };
  }, {});
};
// 获取今日通行次数
const getTransitCount = async () => {
  try {
    const res = await transitCount();
    statisticsData.value.appointmentCount = res?.data?.visitorPassCount || 0;
  } catch (error) {
    console.error(error);
  }
}

// 统一管理所有图表的loading状态
const setAllChartsLoading = (isLoading) => {
  loading.value = isLoading;
  if (appionPieChartRef.value) appionPieChartRef.value.loading = isLoading;
  if (arriveChartRef.value) arriveChartRef.value.loading = isLoading;
  if (objChartRef.value) objChartRef.value.loading = isLoading;
};

// 获取通行趋势数据
const getTransitTrends = async () => {
  // 参数验证
  if (!formData.value.dateRange || formData.value.dateRange.length !== 2) {
    console.warn('日期范围参数无效');
    return;
  }

  try {
    setAllChartsLoading(true);
    
    const params = {
      startDate: formData.value.dateRange[0],
      endDate: formData.value.dateRange[1]
    };
    
    const res = await transitTrends(params);
    const info = res?.data || {};
    
    if (Object.keys(info).length > 0) {
      upDateInfo(info);
    } else {
      console.warn('未获取到有效数据');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
  } finally {
    setAllChartsLoading(false);
  }
};

const upDateInfo = (info) => {
  const { transitTrends, appointmentStatusCount, arriveStatusCount, objCount} = info;
  trendData.value = transitTrends || [];
  appointmentStatusT.value = appointmentStatusCount || [];
  arriveStatusT.value = arriveStatusCount || [];
  objCountT.value = objCount || [];
  upTransitTrend(trendData.value)
  let appointData = handlePieData('status', 'count', appointmentStatusT.value, appointmentStatusMap)
  infoMap1.value = getInfoMap(appointData)

  let arriveSData = handlePieData('status', 'count', arriveStatusT.value, arriveStatusMap)
  infoMap2.value = getInfoMap(arriveSData)

  let objCT = handlePieData('objName', 'count', objCountT.value)
  infoMap3.value = getInfoMap(objCT)

  nextTick(() => {
    appionPieChartRef.value.upDate(appointData)
    arriveChartRef.value.upDate(arriveSData)
    objChartRef.value.upDate(objCT)
  })
}
const upTransitTrend = (list=[]) => {
  if (list.length > 0) {
    let xAxisData = list.map(item => item?.date??'未知日期');
    // let xAxisData = ["7-18", "7-19", "7-20", "7-21", "7-22", "7-23", "7-24"];
    let data = list.map(item => item?.count??0);
    // let data = [25, 20, 15, 20, 28, 25, 20];
    trendChartInstance.value.setOption({
      xAxis: {
        data: xAxisData
      },
      series: [
        {
          data: data
        }
      ]
    })
  }
}
const handlePieData = (name='', value='count', list=[], mappper) => {
  if (!Array.isArray(list)) return [];
  let newList = list.map(item => {
    if (mappper) {
      return { name: mappper[item[name]], value: item[value] }
    } else {
      return { name: item[name], value: item[value] };
    }
  })
  return newList;
}

// 初始化趋势图
const initTrendChart = () => {
  if (!trendChartRef.value) return;
  // 销毁旧实例（避免重复初始化）
  if (trendChartInstance.value) trendChartInstance.value.dispose();
  trendChartInstance.value = echarts.init(trendChartRef.value);
  const option = {
    tooltip: {
      trigger: "axis",
      show: true,
      backgroundColor: "#7B7B7B",
      textStyle: {
        color: "#FBFBFB"
      },
    },
    grid: {
      left: "3%",
      right: "10%",
      bottom: "1%",
      top: "15%",
      containLabel: true
    },
    dataZoom: [
      {
        type: "inside"
      }
    ],
    xAxis: {
      type: "category",
      boundaryGap: false,
      name: "日期",
      axisPointer: {
        snap: true,
        lineStyle: {
          color: "#111111FF",
          type: "line"
        }
      },
      data: []
    },
    yAxis: {
      type: "value",
      name: "单位：人/次",
      minInterval: 1,
      nameLocation: "middle",
      nameTextStyle: {
        fontSize: fitChatSizeVw(12)
      },
      nameGap: fitChatSizeVw(30),
      splitLine: {
        lineStyle: {
          type: "dashed"
        }
      }
    },
    series: [
      {
        name: "通行人数",
        type: "line",
        symbol: "circle",
        smooth: true,
        showSymbol: false,
        lineStyle: {
          color: "#545353"
        },
        itemStyle: {
          borderWidth: fitChatSizeVw(3),
          color: "#AA011C"
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(84, 83, 83, 0.2)"
            },
            {
              offset: 1,
              color: "rgba(123, 123, 123, 0)"
            }
          ])
        },
        data: []
      }
    ]
  };
  trendChartInstance.value.setOption(option);
};

// 日期变化处理
const handleDateChange = val => {
  if (val && val.length === 2) {
    debouncedGetTransitTrends()
  }
};

// Watch for date changes
watch(
  () => formData.value.dateRange,
  newVal => {
    if (newVal && newVal.length === 2) {
      handleDateChange(newVal)
    }
  }
);

// Handle window resize
const handleResize = debounce(() => {
  trendChartInstance.value?.resize();
}, 300);

// 组件挂载后初始化图表
onMounted(() => {
  nextTick(() => {
    initTrendChart();
    window.addEventListener("resize", handleResize);
    getTransitCount();
    getTransitTrends() // 初始化时直接调用，不使用防抖
  });
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  trendChartInstance.value?.dispose(); // 销毁趋势图实例
});

const debouncedGetTransitTrends = debounce(getTransitTrends, 300);
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.passContainer {
  height: 100%;
  width: 49.5%;
  .title-wrapper {
    display: flex;
    align-items: center;
    gap: vw(8);
    .title-bar {
      width: vw(4);
      height: vh(15);
      background: #aa011c;
      border-radius: 2px;
    }
    .section-title {
      font-size: $vw16;
      color: #333;
      font-weight: 500;
      margin: 0;
    }
  }
  .el-card {
    height: 100%;
    :deep(.el-card__header) {
      border-bottom: 0 !important;
    }
  }
  .trend-card {
    flex: 1;
    padding: vw(15) vh(15);
    border: 0;
    margin-bottom: vh(10);
    border-radius: vw(8);
    background: rgba(229, 228, 228, 0.15);
    .top-height {
      height: vh(25);
    }
    .trend-chart {
      height: calc(100% - vh(25));
    }
  }

  .pie-charts {
    display: flex;
    gap: vw(20);

    .pie-card {
      padding: vw(15) vh(15);
      border: 0;
      flex: 1;
      border-radius: vw(8);
      background: rgba(229, 228, 228, 0.15);
      .card-header {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: $vw16;
      }

      .pie-chart {
        height: 10vh;
      }
    }
  }
}
.el-card {
  &.pass-card {
    :deep(.el-card__body) {
      display: flex;
      flex-direction: column;
      height: 100% !important;
    }
  }
}
</style>
