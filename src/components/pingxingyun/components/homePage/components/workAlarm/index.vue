<template>
  <div class="alarmContainer">
    <el-card shadow="hover" class="alarm-card">
      <div class="alarm-content">
        <!-- Top Section with Filter and Statistics -->
        <div class="top-section">
          <!-- Statistics Cards -->
          <div class="statistics-row">
            <div v-for="(item, index) in statistics" :key="index" class="stat-card">
              <div class="stat-info">
                <span class="stat-title">{{ item.title }}</span>
                <div class="stat-value-container">
                  <span class="stat-value">{{ item.value }}</span>
                  <span class="stat-unit" v-if="item.unit">{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
          <!-- Date Filter Section -->
          <div class="filter-section">
            <el-form :model="formData" ref="formRef" label-width="100px">
              <el-form-item label="选择日期" prop="dateRange">
                <el-date-picker v-model="formData.dateRange" type="daterange" range-separator="-"
                  start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" :disabledDate="disabledDate"
                  @change="handleDateChange" />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
          <!-- Trend Chart -->
          <div class="chart-card">
            <div class="title-wrapper">
              <div class="title-bar"></div>
              <h3 class="chart-title">告警数量趋势</h3>
            </div>
            <div ref="trendChartRef" class="chart-container" v-loading="loading"></div>
          </div>

          <!-- Pie Chart -->
          <div class="chart-card">
            <div class="title-wrapper">
              <div class="title-bar"></div>
              <h3 class="chart-title">告警处理分析</h3>
            </div>
            <div ref="pieChartRef" class="chart-container" v-loading="loading"></div>
          </div>

          <!-- Time Distribution Chart -->
          <div class="chart-card" style="position: relative">
            <div class="title-wrapper">
              <div class="title-bar"></div>
              <h3 class="chart-title">告警处理时效</h3>
            </div>
            <div ref="timeChartRef" class="chart-container" v-loading="loading"></div>
            <div class="average-time">
              <img src="./longtime.png" alt="时长" class="time-icon" />
              <div class="time-info">
                <div class="time-label">平均时长</div>
                <div class="time-value">{{getAvgDisposeTime}}小时</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { dispatch_getSystemAlarmCount } from "@/api/dispatch";
import { fitChatSizeVh, fitChatSizeVw } from "@/utils/echartResize";
import * as echarts from "echarts";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
// Form data
const formRef = ref(null);
const formData = ref({
  dateRange: ""
});

// 获取平均处理时长
let getAvgDisposeTime = ref(null);
// Chart references
const trendChartRef = ref(null);
const pieChartRef = ref(null);
const timeChartRef = ref(null);
let trendChart = null;
let pieChart = null;
let timeChart = null;

// 添加loading状态
const loading = ref(false);

const getSystemAlarmCount = async (startDate, endDate) => {
  try {
    loading.value = true;
    const res = await dispatch_getSystemAlarmCount(startDate, endDate);
    if (res.code === 200) {
      const apiData = res.data;
      getAvgDisposeTime = res.data.avgDisposeTime;

      // 计算总处理数（避免除数为0）
      const totalDisposed = apiData.disposedCount || 1; // 如果为0则使用1作为除数

      // 转换API数据为组件所需格式
      const formattedData = {
        statistics: {
          total: apiData.alarmCount || 0,
          processed: apiData.disposedCount || 0,
          unprocessed: apiData.unDisposedCount || 0,
          rate: apiData.responseRate || 0,
          avgTime: apiData.avgDisposeTime || 0
        },
        trend: (apiData.alarmTrends || []).map(item => ({
          value: item.count,
          date: item.date
        })),
        pieData: {
          processed: apiData.disposedCount || 0,
          unprocessed: apiData.unDisposedCount || 0
        },
        timeData: [
          { 
            value: apiData.avgDisposeTimeCount?.count_0_3_hours || 0, 
            name: "3小时", 
            part: apiData.disposedCount ? `${Math.round((apiData.avgDisposeTimeCount?.count_0_3_hours || 0) / totalDisposed * 100)}%` : "0%" 
          },
          { 
            value: apiData.avgDisposeTimeCount?.count_3_6_hours || 0, 
            name: "6小时", 
            part: apiData.disposedCount ? `${Math.round((apiData.avgDisposeTimeCount?.count_3_6_hours || 0) / totalDisposed * 100)}%` : "0%" 
          },
          { 
            value: apiData.avgDisposeTimeCount?.count_6_24_hours || 0, 
            name: "24小时", 
            part: apiData.disposedCount ? `${Math.round((apiData.avgDisposeTimeCount?.count_6_24_hours || 0) / totalDisposed * 100)}%` : "0%" 
          },
          { 
            value: apiData.avgDisposeTimeCount?.count_over_24_hours || 0, 
            name: "超过24小时", 
            part: apiData.disposedCount ? `${Math.round((apiData.avgDisposeTimeCount?.count_over_24_hours || 0) / totalDisposed * 100)}%` : "0%" 
          }
        ]
      };
      
      updateAllData(formattedData);
    }
  } catch (error) {
    console.error('获取告警数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 统计数据
const statistics = ref([
  {
    title: "告警次数",
    value: "134",
    unit: "次"
  },
  {
    title: "已处理数量",
    value: "134",
    unit: "次"
  },
  {
    title: "未处理数量",
    value: "134",
    unit: "次"
  },
  {
    title: "响应率",
    value: "99",
    unit: "%"
  },
  {
    title: "平均响应时长",
    value: "3.2",
    unit: "小时"
  }
]);

// Methods
const handleDateChange = async (val) => {
  if (val && val.length === 2) {
    const [startDate, endDate] = val;
    await getSystemAlarmCount(startDate, endDate);
  }
};

const updateAllData = (data) => {
  // 更新统计数据
  statistics.value = [
    { title: "告警次数", value: data.statistics.total.toString(), unit: "次" },
    { title: "已处理数量", value: data.statistics.processed.toString(), unit: "次" },
    { title: "未处理数量", value: data.statistics.unprocessed.toString(), unit: "次" },
    { title: "响应率", value: data.statistics.rate.toString(), unit: "%" },
    { title: "平均响应时长", value: data.statistics.avgTime.toString(), unit: "小时" }
  ];

  // 确保图表实例存在并更新
  nextTick(() => {
    if (trendChart) {
      initTrendChart(data.trend);
    }
    if (pieChart) {
      initPieChart(data.pieData);
    }
    if (timeChart) {
      initTimeChart(data.timeData);
      console.log('data.timeData',data.timeData);
    }
  });
};

const initTrendChart = (trendData = []) => {
  if (!trendChart) return;

  // 计算dataZoom的起始位置
  const dataLength = trendData.length;
  const showAll = dataLength <= 7;
  const startPercent = showAll ? 0 : ((dataLength - 7) / dataLength) * 100;
  const endPercent = 100;

  // 计算y轴最大值
  const maxValue = Math.max(...trendData.map(item => item.value));
  const yAxisMax = Math.ceil(maxValue * 1.1); // 最大值上浮10%，并向上取整

  const option = {
    backgroundColor: '#fbfbfb',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%', // 增加底部空间以显示x轴标签
      top: '16%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: trendData.map(item => item.date),
      axisLine: {
        show: false,
        lineStyle: {
          color: '#333'
        }
      },
      axisTick: {
        show: false,
        alignWithLabel: true
      },
      axisLabel: {
        show: true,
        interval: 0, // 强制显示所有标签
        rotate: 0, // 旋转角度
        textStyle: {
          color: '#333',
          fontSize:fitChatSizeVw(10),
          fontFamily: 'Microsoft YaHei'
        },
        margin: 15 // 增加标签与轴线的距离
      }
    },
    yAxis: {
      type: 'value',
      max: yAxisMax,
      nameTextStyle: {
        color: '#333'
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#333'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(0,0,0,0.1)'
        }
      },
      axisLabel: {
        color: '#333'
      }
    },
    dataZoom: [{
      show: !showAll,
      height:fitChatSizeVh(12),
      xAxisIndex: [0],
      bottom: '8%',
      start: startPercent,
      end: endPercent,
      handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
      handleSize: '110%',
      handleStyle: {
        color: "#d3dee5",
      },
      textStyle: {
        color: "#333"
      },
      borderColor: "#90979c",
      filterMode: 'filter'
    }, {
      type: "inside",
      show: true,
      xAxisIndex: [0],
      start: startPercent,
      end: endPercent,
      zoomOnMouseWheel: true,
      moveOnMouseMove: true
    }],
    series: [{
      name: '告警数量',
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: '#aa011c'
          }, {
            offset: 1,
            color: '#f8dcdc'
          }]),
          barBorderRadius: [4, 4, 0, 0]
        }
      },
      data: trendData.map(item => ({
        value: item.value,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: '#aa011c'
            }, {
              offset: 1,
              color: '#f8dcdc'
            }]),
            barBorderRadius: [4, 4, 0, 0]
          }
        }
      }))
    }]
  };
  trendChart.setOption(option);
};

const initPieChart = (pieData = { processed: 123, unprocessed: 123 }) => {
  if (!pieChart) return;

  const option = {
    tooltip: {
      trigger: "item",
      confine: true
    },
    series: [
      {
        type: "pie",
        radius: ["50%", "70%"],
        center: ["50%", "50%"],
        data: [
          { value: pieData.processed, name: "已处理", itemStyle: { color: "#AA011C" } },
          { value: pieData.unprocessed, name: "未处理", itemStyle: { color: "#CCCCCC" } }
        ],
        label: {
          position: 'outside',        // 将标签放在饼图外部
          formatter: "{b}\n{c} ({d}%)",     // 简化标签内容
          color: "#333",
          alignTo: 'none',           // 防止标签相互重叠
          distanceToLabelLine: 5     // 调整标签与引导线的距离
        },
        labelLine: {
          length: 10,                // 减少引导线长度 
          length2: 10
        }
      }
    ]
  };
  pieChart.setOption(option);
};

const initTimeChart = (
  timeData = [
    { value: 60, name: "3小时", part: "45%" },
    { value: 40, name: "6小时", part: "45%" },
    { value: 20, name: "24小时", part: "45%" },
    { value: 80, name: "超过24小时", part: "45%" }
  ]
) => {
  if (!timeChart) return;

  const option = {
    series: [
      {
        name: "漏斗图",
        type: "funnel",
        left: "26%",
        top: "5%",
        bottom: "5%",
        width: "80%",
        height: "100%",
        min: 0,
        max: 100,
        minSize: "30%",
        maxSize: "100%",
        sort: "descending",
        gap: 0,
        data: timeData
          .map(item => ({
            ...item,
            itemStyle: {
              color: item.name === "超过24小时" ? "#AA011C" : item.name === "24小时" ? "#F8DCDC" : item.name === "6小时" ? "#FAAE13" : "#CCCCCC"
            },
            label: {
              color: item.name === "超过24小时" ? "#fff" : "#333"
            }
          }))
          .sort((a, b) => a.value - b.value),
        roseType: true,
        label: {
          normal: {
            formatter: function (params) {
              return params.name + " " + "{style|" + params.value + "}" + " " + "{part|" + params.data.part + "}";
            },
            position: "center",
            rich: {
              style: {
                padding: [0, 0, 0, 20]
              },
              part: {
                marginLeft: "4px"
              }
            }
          }
        },
        itemStyle: {
          normal: {
            borderWidth: 0,
            shadowBlur: 30,
            shadowOffsetX: 0,
            shadowOffsetY: 10,
            shadowColor: "rgba(0, 0, 0, 0)"
          }
        }
      }
    ]
  };
  timeChart.setOption(option);
};

// Handle window resize
const handleResize = () => {
  trendChart?.resize();
  pieChart?.resize();
  timeChart?.resize();
};

// Lifecycle hooks
onMounted(() => {
  initCharts();
  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
  trendChart?.dispose();
  pieChart?.dispose();
  timeChart?.dispose();
});

// Watch for date changes
watch(
  () => formData.value.dateRange,
  newVal => {
    handleDateChange(newVal);
  },
  { immediate: true }
); // 添加 immediate: true 以确保初始化时也执行一次

// 禁用今年以外的日期
const disabledDate = time => {
  const currentYear = new Date().getFullYear();
  const year = new Date(time).getFullYear();
  return year !== currentYear;
};

// 初始化图表实例
const initCharts = () => {
  nextTick(() => {
    // 初始化图表实例
    if (trendChartRef.value && !trendChart) {
      trendChart = echarts.init(trendChartRef.value);
    }
    if (pieChartRef.value && !pieChart) {
      pieChart = echarts.init(pieChartRef.value);
    }
    if (timeChartRef.value && !timeChart) {
      timeChart = echarts.init(timeChartRef.value);
    }

    // 获取当前日期
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    // 设置默认日期范围为最近30天
    formData.value.dateRange = [
      thirtyDaysAgo.toISOString().split('T')[0],
      today.toISOString().split('T')[0]
    ];

    // 使用实际日期范围获取数据
    getSystemAlarmCount(formData.value.dateRange[0], formData.value.dateRange[1]);
  });
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";

.alarmContainer {
  height: 35vh;
  width: 100%;

  min-height: vh(300);

  .alarm-card {
    height: 100%;

    :deep(.el-card__body) {
      height: 100%;
      padding: vw(20);
    }
  }

  .alarm-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .top-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: vh(20);
  }

  .filter-section {
    flex: 0 0 auto;
    margin-right: vw(20);

    .el-form {
      margin: 0;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .statistics-row {
    flex: 1;
    display: flex;
    justify-content: space-between;
    gap: vw(15);
  }

  .stat-card {
    flex: 0 1 auto;
    min-width: fit-content;
    padding: vh(8) vw(20);
    border-radius: vw(8);
    background: #fbe9e9;
    transition: all 0.3s ease;
    white-space: nowrap;

    &:hover {
      transform: translateY(-vh(2));
      box-shadow: 0 vw(2) vw(12) rgba(0, 0, 0, 0.1);
    }

    .stat-info {
      display: flex;
      align-items: center;
      gap: vw(12);
    }

    .stat-title {
      font-size: $vw16;
      color: #111111;
      font-weight: 500;
    }

    .stat-value-container {
      display: flex;
      align-items: baseline;
      gap: vw(4);
    }

    .stat-value {
      font-size: vw(24);
      font-weight: bold;
      color: #111111;
    }

    .stat-unit {
      font-size: vw(12);
      color: #111111;
      margin-left: vw(2);
    }
  }

  .charts-section {
    flex: 1;
    display: grid;
    grid-template-columns: 4fr 3fr 3fr;
    gap: vw(20);
    height: calc(100% - vw(120));

    .chart-card {
      background: #fbfbfb;
      border-radius: vw(8);
      padding: vw(15);
      height: 100%;
      display: flex;
      flex-direction: column;
      box-shadow: 0 vw(2) vw(12) rgba(0, 0, 0, 0.05);

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: vw(8);
        // margin-bottom: 15px;

        .title-bar {
          width: vw(4);
          height: vh(15);
          background: #aa011c;
          border-radius: vw(2);
        }

        .chart-title {
          font-size: vw(16);
          color: #333;
          font-weight: 500;
          margin: 0;
        }
      }

      .chart-container {
        height: calc(100% - vh(30)); // 减去title-wrapper的高度
        width: 100%;
        position: relative; // 添加相对定位以支持loading
      }

      .average-time {
        position: absolute;
        top: vh(100);
        left: vw(20);
        display: flex;
        align-items: center;
        gap: vw(8);

        .time-icon {
          width: vw(24);
          height: vh(24);
        }

        .time-info {
          display: flex;
          flex-direction: column;

          .time-label {
            font-size: vw(14);
            color: #666;
            margin-bottom: vh(4);
            font-family: Arial, Helvetica, sans-serif;
          }

          .time-value {
            font-size: vw(16);
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
