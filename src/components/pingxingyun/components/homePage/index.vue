<template>
  <div class="container poi_abs z-in2">
    <div class="flex spaceb firstFloor">
      <PassTrend></PassTrend>
      <WorkType></WorkType>
    </div>
    <WorkAlarm></WorkAlarm>
  </div>
</template>
<script setup>
import PassTrend from "./components/passTrend/index.vue"; //通行趋势
import WorkAlarm from "./components/workAlarm/index.vue"; //告警内容
import WorkType from "./components/workType/index.vue"; //作业类型
</script>
<style lang="scss" scoped>
@import "@/assets/styles/screen.scss";
.container {
  width: 100vw;
  height: 100vh;
  background-color: rgb(240, 240, 240);
  padding: vh(100) vw(20) vh(20);
}
.firstFloor {
  height: calc(65vh - vh(140));
  margin-bottom: vh(20);
}
</style>
