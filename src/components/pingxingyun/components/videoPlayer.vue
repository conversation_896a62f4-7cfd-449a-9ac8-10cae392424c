<template>
  <div class="video-container">
    <video ref="videoRef" autoplay controls :style="{ width: '100%', height: height }" muted />
  </div>
</template>

<script setup>
import flvjs from "flv.js";
import { onMounted, onUnmounted } from "vue";
const prop = defineProps({
  height: {
    type: String,
    default: () => "18vh"
  }
});

const flvPlayer = ref();
const videoRef = ref(null);
const playVideo = videoUrl => {
  nextTick(() => {
    if (flvjs.isSupported() && videoUrl) {
      var videoElement = videoRef.value;

      destroyFlv();

      // const res = await refreshVideo({ id: videoObj.id })

      setTimeout(() => {
        flvPlayer.value = flvjs.createPlayer(
          {
            //type: "flv",
            type: "video/mp4",
            url: videoUrl, // 你的url地址,
            // url: 'https://media.w3.org/2010/05/sintel/trailer.mp4', // 你的url地址,
            // url: 'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv', // 你的url地址,
            hasAudio: false,
            stashInitialSize: 128 // 减少首桢显示等待时长
          },
          {
            enableWorker: false, // 不启用分离线程
            enableStashBuffer: false, // 关闭IO隐藏缓冲区
            reuseRedirectedURL: true, // 重用301/302重定向url，用于随后的请求，如查找、重新连接等。
            autoCleanupSourceBuffer: true, // 自动清除缓存
            fixAudioTimestampGap: false // false才会音视频同步
          }
        );
        // $emit('updata-video-info', res.data.httpStream)
        flvPlayer.value?.attachMediaElement(videoElement);
        flvPlayer.value?.load();
        flvPlayer.value?.play();
      }, 0);
    }
  });
};

// 销毁flv
const destroyFlv = () => {
  if (flvPlayer.value) {
    flvPlayer.value.pause();
    flvPlayer.value.unload();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
  }
};
onMounted(() => {});

onUnmounted(() => {
  destroyFlv();
});
defineExpose({
  playVideo,
  destroyFlv
});
</script>
<style>
.video-container {
  margin-bottom: -3px;
}

#myVideo {
  width: 100%;
}
</style>
