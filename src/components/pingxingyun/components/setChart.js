import * as echarts from "echarts";
import { shallowRef } from "vue";
let colorList = [
  "rgba(254, 225, 134, 0.4)",
  "rgba(165, 216, 252, 0.4)",
  "rgba(102, 146, 255, 0.4)",
  "rgba(111,233,215,0.4)",
  "rgba(157, 154, 252, 0.4)",
  "rgba(154, 96, 180, 0.4)",
  "rgba(234, 124, 204, 0.4)",
  "rgba(183, 212, 56, 0.4)",
  "rgba(172, 56, 212, 0.4)",
  "rgba(142, 211, 73, 0.4)",
  "rgba(126, 184, 44, 0.4)",
  "rgba(118, 195, 222, 0.4)",
  "rgba(145, 118, 222, 0.4)",
  "rgba(63, 50, 67, 0.4)"
];
let colorListOut = [
  "rgba(254, 225, 134, 1)",
  "rgba(165, 216, 252, 1)",
  "rgba(102, 146, 255, 1)",
  "rgba(111,233,215,1)",
  "rgba(157, 154, 252, 1)",
  "rgba(154, 96, 180, 1)",
  "rgba(234, 124, 204, 1)",
  "rgba(183, 212, 56, 1)",
  "rgba(172, 56, 212, 1)",
  "rgba(142, 211, 73, 1)",
  "rgba(126, 184, 44, 1)",
  "rgba(118, 195, 222, 1)",
  "rgba(145, 118, 222, 1)",
  "rgba(63, 50, 67, 1)"
];

// 公用调整
let itemStyle = {
  normal: {
    // borderColor: "#fff",
    //  borderWidth: 4,
    color: function (params) {
      return colorList[params.dataIndex];
    }
  }
};
let itemStyleOut = {
  normal: {
    // borderColor: "#fff",
    //  borderWidth: 4,
    color: function (params) {
      return colorListOut[params.dataIndex];
    }
  }
};
export const useChartData = () => {
  const domChart = shallowRef(null);
  const domRef = shallowRef(null);
  let observeResize = ref(null);
  const initCharts = option => {
    domChart.value = echarts.init(domRef.value, "macarons");
    domChart.value.setOption(option);
    observeResize.value = new ResizeObserver(entries => {
      for (let entry in entries) {
        domChart.value.resize();
      }
    });
    observeResize.value.observe(domRef.value);
    // window.addEventListener("resize", function () {
    //   domChart.value.resize();
    // });
  };
  // onUnmounted(() => {
  //
  //   observeResize.value.unobserve(domRef.value);
  // });
  return {
    itemStyle,
    itemStyleOut,
    domRef,
    domChart,
    initCharts
  };
};
