import defaultSettings from "@/settings";
import usePermissionStore from "@/store/modules/permission";
import useSettingsStore from "@/store/modules/settings";
import useUserStore from "@/store/modules/user";
import { getToken, setToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";
import { isHttp } from "@/utils/validate";
import { ElMessage } from "element-plus";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import router from "./router";
NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/401", "/register"];

//修改单点登录 2025-03-20
router.beforeEach(async (to, from, next) => {
  NProgress.start();

  try {
    // 处理带 code 参数的登录流程
    if (to.query.token) {
      await handleCodeLogin(to, next);
      return;
    }
    // 主流程处理
    console.log(1);
    const hasToken = getToken();
    if (hasToken) {
      await handleAuthFlow(to, next);
    } else {
      console.log(2);
      handleNoAuthFlow(to, next);
    }
  } catch (err) {
    handleError(err, next);
  } finally {
    NProgress.done();
  }
});

// 新增工具函数

const getLoginUrl = async () => {
  const loginUrl = await getConfigKey("sys.redirect.url");
  return loginUrl;
};
const handleCodeLogin = async (to, next) => {
  ///await useUserStore().login(to.query);
  setToken(to.query.token);
  delete to.query.token;
  useSettingsStore().setTitle(to.meta.title);
  if (!getToken()) {
    throw new Error("单点登录失败");
  }
  //next();

  // if (to.path === "/login") {
  //   return next("/");
  // }

  // if (whiteList.includes(to.path)) {
  //   return next();
  // }
  // next();
  await handleAuthFlow(to, next);
};

const handleAuthFlow = async (to, next) => {
  useSettingsStore().setTitle(to.meta.title);

  if (to.path === "/login") {
    return next("/");
  }

  if (whiteList.includes(to.path)) {
    return next();
  }

  const userStore = useUserStore();
  if (userStore.roles.length === 0) {
    await loadUserInfoAndRoutes(to, next);
  } else {
    await userStore.getToDoNoticeList().catch(() => {});
    next();
  }
};

const loadUserInfoAndRoutes = async (to, next) => {
  isRelogin.show = true;
  try {
    await useUserStore().getInfo();
    //await addAccessRoutes();
    next({ ...to, replace: true });
  } finally {
    isRelogin.show = false;
  }
};

const addAccessRoutes = async () => {
  const accessRoutes = await usePermissionStore().generateRoutes();
  accessRoutes.filter(route => !isHttp(route.path)).forEach(route => router.addRoute(route));
};

const handleNoAuthFlow = async (to, next) => {
  console.log(5);
  if (whiteList.includes(to.path)) {
    return next();
  }

  // const loginPath = to.meta.isEnable === "1" ? { path: "/login", query: { redirect: to.fullPath } } : "/login";

  // const forbiddenPath = "/401";

  // next(forbiddenPath);
  let baseUrl = defaultSettings.redirectUrl;
  let params = new URLSearchParams({
    response_type: "code",
    // client_id: "d2034373-fc2f-40c9-a9e7-0a203be2f125",//测试环境
    client_id: import.meta.env.VITE_APP_CLIENTID,
    // redirect_uri: window.location.origin + "/jsyp-frontend-web-sso"
    redirect_uri: `${import.meta.env.VITE_APP_REDIRECT_URL}/jsyp-frontend-web-sso?redirect=https://tx.jinshiyuan.com.cn/jsyp-frontend-big-screen/index`
  });
  let fullUrl = `${baseUrl}?${params.toString()}`;
  // // debugger;
  location.href = fullUrl;
  // console.log(fullUrl);
  // const redirectUrl = await getLoginUrl();
  // window.location.href = redirectUrl.msg;

  next();
};

const handleError = (err, next) => {
  ElMessage.error(err.message);
  useUserStore().logOut();
  next("/");
};

router.afterEach(() => {
  NProgress.done();
});
