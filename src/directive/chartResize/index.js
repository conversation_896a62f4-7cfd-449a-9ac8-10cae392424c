import * as echarts from "echarts";

import elementResizeDetectorMaker from "element-resize-detector";
let rgd = elementResizeDetectorMaker();
export default {
  beforeMount(el, binding) {
    rgd.listenTo(el, () => {
      const chart = echarts.getInstanceByDom(el);
      if (!chart) return;
      chart.resize();
      // if (binding.value) {
      //   binding.value(element);
      // }
    });
  },
  onUnmounted(el) {
    rgd.removeListener(el);
  }
};
