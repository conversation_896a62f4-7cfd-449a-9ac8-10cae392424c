module.exports = {
  // 一行最多多少个字符
  printWidth: 150,
  // 使用制表符而不是空格缩进行
  useTabs: false,
  // 指定每个缩进级别的空格数
  tabWidth: 2,
  jsxSingleQuote: false,
  singleQuote: false,
  // 换行符使用 lf 结尾是 可选值"<auto|lf|crlf|cr>"
  endOfLine: 'auto',
  // 在语句末尾是否需要分号
  semi: true,
  trailingComma: 'none',
  // 指定HTML文件的全局空格敏感度 css\strict\ignore
  htmlWhitespaceSensitivity: 'css',
  // 在单独的箭头函数参数周围包括括号 always：(x) => x \ avoid：x => x
  arrowParens: 'avoid'
};
