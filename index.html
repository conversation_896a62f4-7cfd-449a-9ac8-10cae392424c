<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title>今世缘酒业智慧园区</title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <!-- <script type="text/javascript" src="/static/js/jquery.min.js"></script> -->
  <!-- <script type="text/javascript" src="/static/js/lark-iframe-poster.js" defer></script> -->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #0C72C7;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #0C72C7;
      opacity: 0.5;
    }

    #loader-wrapper .loaderBgBox {
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      /* display: flex;
      align-items: center;
      justify-content: center; */
    }

    #loader-wrapper .loaderBgBox .loaderBg {
      position: relative;
      left: 0;
      right: 0;
      margin: 0 auto;
      /* z-index: 9999999999999; */
      width: 20%;
      height: 40%;
      top: 10%;
      background-image: url("./src/assets/images/loading.png");
      background-size: cover;
    }

    .line div {
      position: absolute;
      right: 68px;
      top: 54px;
      width: 4px;
      height: 50px;
    }


    .line div:before,
    .line div:after {
      content: '';
      display: block;
      height: 50%;
      background: RGBA(189, 223, 255, 1);
      border-radius: 5px;
    }

    /*设置组成环形加载的竖条的旋转角度*/
    .line div:nth-child(2) {
      transform: rotate(45deg);
    }


    .line div:nth-child(3) {
      transform: rotate(90deg);
    }


    .line div:nth-child(4) {
      transform: rotate(135deg);
    }


    .line div:nth-child(5) {
      transform: rotate(180deg);
    }

    .line div:nth-child(6) {
      transform: rotate(225deg);
    }

    .line div:nth-child(7) {
      transform: rotate(270deg);
    }


    .line div:nth-child(8) {
      transform: rotate(315deg);
    }


    /* .line div:nth-child(9) {
      transform: rotate(120deg);
    }


    .line div:nth-child(10) {
      transform: rotate(135deg);
    }

    .line div:nth-child(11) {
      transform: rotate(150deg);
    }

    .line div:nth-child(12) {
      transform: rotate(165deg);
    } */

    .circle {
      position: absolute;
      /* left: -15%; */
      right: 58px;
      top: 66px;
      width: 25px;
      height: 25px;
      /* margin: -9px 0 0 -9px; */
      background: #fff;
      border-radius: 100%;
    }

    /*定义动画*/
    @keyframes load {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }

    /*设置动画以及动画延迟 */
    .line div:nth-child(1):before {
      animation: load 0.4s linear 0s infinite;
    }

    /*依次从第一个div的:before至最后一个div的:before的动画延迟为每个增加0.05s,此处省略雷同代码*/
    .line div:nth-child(2):before {
      animation: load 0.4s linear 0.025s infinite;
    }

    .line div:nth-child(3):before {
      animation: load 0.4s linear 0.05s infinite;
    }

    .line div:nth-child(4):before {
      animation: load 0.4s linear 0.075s infinite;
    }

    .line div:nth-child(5):before {
      animation: load 0.4s linear 0.1s infinite;
    }

    .line div:nth-child(6):before {
      animation: load 0.4s linear 0.125s infinite;
    }

    .line div:nth-child(7):before {
      animation: load 0.4s linear 0.15s infinite;
    }

    .line div:nth-child(8):before {
      animation: load 0.4s linear 0.175s infinite;
    }

    /* .line div:nth-child(9):before {
      animation: load 1.2s linear 0.4s infinite;
    }

    .line div:nth-child(10):before {
      animation: load 1.2s linear 0.45s infinite;
    }

    .line div:nth-child(11):before {
      animation: load 1.2s linear 0.5s infinite;
    }

    .line div:nth-child(12):before {
      animation: load 1.2s linear 0.55s infinite;
    } */

    .line div:nth-child(1):after {
      animation: load 0.4s linear 0.2s infinite;
    }

    /*依次从第一个div的:after至最后一个div的:after的动画延迟为每个增加0.05s,此处省略雷同代码*/
    .line div:nth-child(2):after {
      animation: load 0.4s linear 0.225s infinite;
    }

    .line div:nth-child(3):after {
      animation: load 0.4s linear 0.25s infinite;
    }

    .line div:nth-child(4):after {
      animation: load 0.4s linear 0.275s infinite;
    }

    .line div:nth-child(5):after {
      animation: load 0.4s linear 0.3s infinite;
    }

    .line div:nth-child(6):after {
      animation: load 0.4s linear 0.325s infinite;
    }

    .line div:nth-child(7):after {
      animation: load 0.4s linear 0.35s infinite;
    }

    .line div:nth-child(8):after {
      animation: load 0.4s linear 0.375s infinite;
    }

    /* .line div:nth-child(9):after {
      animation: load 1.2s linear 1.0s infinite;
    }

    .line div:nth-child(10):after {
      animation: load 1.2s linear 1.05s infinite;
    }

    .line div:nth-child(11):after {
      animation: load 1.2s linear 1.1s infinite;
    }

    .line div:nth-child(12):after {
      animation: load 1.2s linear 1.15s infinite;
    } */
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <!-- <div id="loader"></div> -->
      <!-- <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div> -->
      <div class="loaderBgBox">
        <div class="loaderBg">
          <div class="line">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <!-- <div></div>
            <div></div>
            <div></div>
            <div></div> -->
          </div>
          <div class="circle"></div>
        </div>

        <div class="load_title">正在加载系统资源，请耐心等待</div>
      </div>

    </div>
  </div>
  <%- injectScript %>
    <script type="module" src="/src/main.js"></script>


</body>

</html>