module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: ["eslint:recommended", "plugin:vue/vue3-essential", "./.eslintrc-auto-import.json", "plugin:prettier/recommended", "prettier"],
  overrides: [
    {
      files: ["src/**/*.vue"],
      rules: { "vue/multi-word-component-names": "off" }
    },
    {
      files: "*.html"
      // options: {
      //   parser: "html"
      // }
    }
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module"
  },
  plugins: ["vue", "html"],
  rules: {
    // indent: ["error", 2, { SwitchCase: 0 }],
    // "linebreak-style": [0, "error", "windows"],
    // quotes: ["error", "double"],
    quotes: ["error", "double", { avoidEscape: true }],
    semi: ["error", "always"],
    "no-unused-vars": "off",
    "no-console": "off",
    "no-debugger": "off",
    "vue/multi-word-component-names": "off"
  }
};
