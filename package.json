{"name": "cfcec", "version": "0.0.1", "description": "今世缘通行管理", "author": "cfcec", "license": "MIT", "type": "module", "scripts": {"dev": "vite serve --mode development", "build:prod": "vite build --mode production", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "http://**************:10089/cParkProjects/cfcec/cfcecFrontend.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@rollup/plugin-commonjs": "^25.0.7", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.6.1", "ant-design-vue": "^4.1.2", "await-to-js": "^3.0.0", "axios": "0.27.2", "bpmn-js": "^11.4.1", "crypto-js": "^4.2.0", "diagram-js": "^11.9.1", "echarts": "5.5.0", "echarts-gl": "^2.0.9", "element-plus": "2.4.3", "element-resize-detector": "^1.2.4", "file-saver": "2.0.5", "flv.js": "^1.6.2", "fuse.js": "6.6.2", "highlight.js": "11.7.0", "js-base64": "^3.7.5", "js-cookie": "3.0.5", "jsencrypt": "^3.3.2", "nprogress": "0.2.0", "pinia": "2.1.7", "v-viewer": "^3.0.11", "vite-plugin-html": "^3.2.1", "vkbeautify": "^0.99.3", "vue": "3.3.9", "vue-cropper": "1.1.1", "vue-router": "4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "4.5.0", "@vue/compiler-sfc": "3.3.9", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "rollup-plugin-visualizer": "^5.11.0", "sass": "1.69.5", "unplugin-auto-import": "0.17.1", "unplugin-vue-components": "^0.26.0", "unplugin-vue-setup-extend-plus": "1.0.0", "vite": "5.0.4", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}