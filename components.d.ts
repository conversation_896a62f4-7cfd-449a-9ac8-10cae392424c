/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Pingxingyun: typeof import('./src/components/pingxingyun/index.vue')['default']
    PingxingyunComponentsCarbonCheck: typeof import('./src/components/pingxingyun/components/carbon/check/index.vue')['default']
    PingxingyunComponentsCarbonCheckEcharts: typeof import('./src/components/pingxingyun/components/carbon/check/echarts/index.vue')['default']
    PingxingyunComponentsCarbonManage: typeof import('./src/components/pingxingyun/components/carbonManage.vue')['default']
    PingxingyunComponentsCarbonMore: typeof import('./src/components/pingxingyun/components/carbon/more/index.vue')['default']
    PingxingyunComponentsCarbonMoreEcharts: typeof import('./src/components/pingxingyun/components/carbon/more/echarts/index.vue')['default']
    PingxingyunComponentsCarbonMoreGauge: typeof import('./src/components/pingxingyun/components/carbon/more/gauge/index.vue')['default']
    PingxingyunComponentsCarbonNeutral: typeof import('./src/components/pingxingyun/components/carbon/neutral/index.vue')['default']
    PingxingyunComponentsCarbonNeutralEcharts: typeof import('./src/components/pingxingyun/components/carbon/neutral/echarts/index.vue')['default']
    PingxingyunComponentsCarbonOverview: typeof import('./src/components/pingxingyun/components/carbon/overview/index.vue')['default']
    PingxingyunComponentsCarbonOverviewEcharts: typeof import('./src/components/pingxingyun/components/carbon/overview/echarts/index.vue')['default']
    PingxingyunComponentsCarbonPeak: typeof import('./src/components/pingxingyun/components/carbon/peak/index.vue')['default']
    PingxingyunComponentsCarbonPeakEcharts: typeof import('./src/components/pingxingyun/components/carbon/peak/echarts/index.vue')['default']
    PingxingyunComponentsCarbonRanking: typeof import('./src/components/pingxingyun/components/carbon/ranking/index.vue')['default']
    PingxingyunComponentsCarbonRankingEcharts: typeof import('./src/components/pingxingyun/components/carbon/ranking/echarts/index.vue')['default']
    PingxingyunComponentsCarbonSink: typeof import('./src/components/pingxingyun/components/carbon/sink/index.vue')['default']
    PingxingyunComponentsCarbonTrend: typeof import('./src/components/pingxingyun/components/carbon/trend/index.vue')['default']
    PingxingyunComponentsCarbonTrendEcharts: typeof import('./src/components/pingxingyun/components/carbon/trend/echarts/index.vue')['default']
    PingxingyunComponentsCarManage: typeof import('./src/components/pingxingyun/components/carManage.vue')['default']
    PingxingyunComponentsDeviceAlarmNotice: typeof import('./src/components/pingxingyun/components/device/alarmNotice.vue')['default']
    PingxingyunComponentsDeviceAlarmStatistics: typeof import('./src/components/pingxingyun/components/device/alarmStatistics.vue')['default']
    PingxingyunComponentsDeviceAlarmStatisticsView: typeof import('./src/components/pingxingyun/components/device/alarmStatisticsView.vue')['default']
    PingxingyunComponentsDeviceAreaControl: typeof import('./src/components/pingxingyun/components/device/areaControl.vue')['default']
    PingxingyunComponentsDeviceCountInfo: typeof import('./src/components/pingxingyun/components/device/countInfo.vue')['default']
    PingxingyunComponentsDeviceDeviceAndFloor: typeof import('./src/components/pingxingyun/components/device/deviceAndFloor.vue')['default']
    PingxingyunComponentsDeviceDeviceInfo: typeof import('./src/components/pingxingyun/components/device/deviceInfo.vue')['default']
    PingxingyunComponentsDeviceDeviceType: typeof import('./src/components/pingxingyun/components/device/deviceType.vue')['default']
    PingxingyunComponentsDeviceDeviceView: typeof import('./src/components/pingxingyun/components/device/deviceView.vue')['default']
    PingxingyunComponentsDeviceFaultRecode: typeof import('./src/components/pingxingyun/components/device/faultRecode.vue')['default']
    PingxingyunComponentsDeviceFaultRecodeView: typeof import('./src/components/pingxingyun/components/device/faultRecodeView.vue')['default']
    PingxingyunComponentsDeviceInspectionRecode: typeof import('./src/components/pingxingyun/components/device/inspectionRecode.vue')['default']
    PingxingyunComponentsDeviceLift: typeof import('./src/components/pingxingyun/components/device/lift.vue')['default']
    PingxingyunComponentsDeviceMaintainRecode: typeof import('./src/components/pingxingyun/components/device/maintainRecode.vue')['default']
    PingxingyunComponentsDeviceMaintainRecodeView: typeof import('./src/components/pingxingyun/components/device/maintainRecodeView.vue')['default']
    PingxingyunComponentsDeviceMonitor: typeof import('./src/components/pingxingyun/components/deviceMonitor.vue')['default']
    PingxingyunComponentsDevicePlayer: typeof import('./src/components/pingxingyun/components/device/player.vue')['default']
    PingxingyunComponentsDeviceRunning: typeof import('./src/components/pingxingyun/components/device/running.vue')['default']
    PingxingyunComponentsDialogMoudle: typeof import('./src/components/pingxingyun/components/dialogMoudle.vue')['default']
    PingxingyunComponentsInfoRemind: typeof import('./src/components/pingxingyun/components/infoRemind.vue')['default']
    PingxingyunComponentsMoudule: typeof import('./src/components/pingxingyun/components/moudule.vue')['default']
    PingxingyunComponentsOperateButton: typeof import('./src/components/pingxingyun/components/operateButton.vue')['default']
    PingxingyunComponentsOtherModule: typeof import('./src/components/pingxingyun/components/otherModule.vue')['default']
    PingxingyunComponentsOverViewDeviceCount: typeof import('./src/components/pingxingyun/components/overView/deviceCount.vue')['default']
    PingxingyunComponentsOverViewDeviceNotice: typeof import('./src/components/pingxingyun/components/overView/deviceNotice.vue')['default']
    PingxingyunComponentsOverViewOrderTotal: typeof import('./src/components/pingxingyun/components/overView/orderTotal.vue')['default']
    PingxingyunComponentsOverViewParkMonitor: typeof import('./src/components/pingxingyun/components/overView/parkMonitor.vue')['default']
    PingxingyunComponentsOverViewParkView: typeof import('./src/components/pingxingyun/components/overView/parkView.vue')['default']
    PingxingyunComponentsOverViewPatrolContent: typeof import('./src/components/pingxingyun/components/overView/patrolContent.vue')['default']
    PingxingyunComponentsOverViewRoomUse: typeof import('./src/components/pingxingyun/components/overView/roomUse.vue')['default']
    PingxingyunComponentsOverViewSchedule: typeof import('./src/components/pingxingyun/components/overView/schedule.vue')['default']
    PingxingyunComponentsOverViewUseEnergy: typeof import('./src/components/pingxingyun/components/overView/useEnergy.vue')['default']
    PingxingyunComponentsParkingAreaPerson: typeof import('./src/components/pingxingyun/components/parking/areaPerson.vue')['default']
    PingxingyunComponentsParkingCarAndGate: typeof import('./src/components/pingxingyun/components/parking/carAndGate.vue')['default']
    PingxingyunComponentsParkingCarDwellTime: typeof import('./src/components/pingxingyun/components/parking/carDwellTime.vue')['default']
    PingxingyunComponentsParkingCarInfo: typeof import('./src/components/pingxingyun/components/parking/carInfo.vue')['default']
    PingxingyunComponentsParkingEnterAndExitCar: typeof import('./src/components/pingxingyun/components/parking/enterAndExitCar.vue')['default']
    PingxingyunComponentsParkingEnterAndExitPerson: typeof import('./src/components/pingxingyun/components/parking/enterAndExitPerson.vue')['default']
    PingxingyunComponentsParkingEnterCarDistribution: typeof import('./src/components/pingxingyun/components/parking/enterCarDistribution.vue')['default']
    PingxingyunComponentsParkingFloorAndArea: typeof import('./src/components/pingxingyun/components/parking/floorAndArea.vue')['default']
    PingxingyunComponentsParkingParkingCamera: typeof import('./src/components/pingxingyun/components/parking/parkingCamera.vue')['default']
    PingxingyunComponentsParkingParkingType: typeof import('./src/components/pingxingyun/components/parking/parkingType.vue')['default']
    PingxingyunComponentsParkingPeopleType: typeof import('./src/components/pingxingyun/components/parking/peopleType.vue')['default']
    PingxingyunComponentsParkingPersonAndGate: typeof import('./src/components/pingxingyun/components/parking/personAndGate.vue')['default']
    PingxingyunComponentsParkingSpacePark: typeof import('./src/components/pingxingyun/components/parking/spacePark.vue')['default']
    PingxingyunComponentsParkingTodayData: typeof import('./src/components/pingxingyun/components/parking/todayData.vue')['default']
    PingxingyunComponentsParkOverView: typeof import('./src/components/pingxingyun/components/parkOverView.vue')['default']
    PingxingyunComponentsSafeCamera: typeof import('./src/components/pingxingyun/components/safe/camera/index.vue')['default']
    PingxingyunComponentsSafeCameraAlarmCamera: typeof import('./src/components/pingxingyun/components/safe/camera/alarmCamera.vue')['default']
    PingxingyunComponentsSafeCameraSetCameraDialog: typeof import('./src/components/pingxingyun/components/safe/camera/setCameraDialog.vue')['default']
    PingxingyunComponentsSafeDeviceInfo: typeof import('./src/components/pingxingyun/components/safe/deviceInfo.vue')['default']
    PingxingyunComponentsSafeFloorControl: typeof import('./src/components/pingxingyun/components/safe/floorControl.vue')['default']
    PingxingyunComponentsSafeManage: typeof import('./src/components/pingxingyun/components/safeManage.vue')['default']
    PingxingyunComponentsSafeSafeAlarm: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/index.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsAlarmChart: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/alarmChart.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsAlarmHundleJudge: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/alarmHundleJudge.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsAlarmHundleWay: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/alarmHundleWay.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsEmergencyInfo: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/emergencyInfo.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsEmergencyStart: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/emergencyStart.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsEmergencyType: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/emergencyType.vue')['default']
    PingxingyunComponentsSafeSafeAlarmComponentsPlanType: typeof import('./src/components/pingxingyun/components/safe/safeAlarm/components/planType.vue')['default']
    PingxingyunComponentsSafeSafeCameras: typeof import('./src/components/pingxingyun/components/safe/safeCameras.vue')['default']
    PingxingyunComponentsSpaceAlarmInfo: typeof import('./src/components/pingxingyun/components/space/alarmInfo.vue')['default']
    PingxingyunComponentsSpaceCurTime: typeof import('./src/components/pingxingyun/components/space/curTime.vue')['default']
    PingxingyunComponentsSpaceFloorList: typeof import('./src/components/pingxingyun/components/space/floorList.vue')['default']
    PingxingyunComponentsSpaceFloorNum: typeof import('./src/components/pingxingyun/components/space/floorNum.vue')['default']
    PingxingyunComponentsSpaceFloors: typeof import('./src/components/pingxingyun/components/space/floors.vue')['default']
    PingxingyunComponentsSpaceFloorType: typeof import('./src/components/pingxingyun/components/space/floorType.vue')['default']
    PingxingyunComponentsSpaceHomeOperate: typeof import('./src/components/pingxingyun/components/space/homeOperate.vue')['default']
    PingxingyunComponentsSpaceManage: typeof import('./src/components/pingxingyun/components/spaceManage.vue')['default']
    PingxingyunComponentsSpaceRoomInfo: typeof import('./src/components/pingxingyun/components/space/roomInfo.vue')['default']
    PingxingyunComponentsSpaceTempAndHum: typeof import('./src/components/pingxingyun/components/space/tempAndHum.vue')['default']
    PingxingyunComponentsVideoPlayer: typeof import('./src/components/pingxingyun/components/videoPlayer.vue')['default']
    PingxingyunComponentsWorkOrderManage: typeof import('./src/components/pingxingyun/components/workOrderManage.vue')['default']
    PingxingyunComponentsWorkOrderOrderInfo: typeof import('./src/components/pingxingyun/components/workOrder/orderInfo.vue')['default']
    PingxingyunComponentsWorkOrderOrderList: typeof import('./src/components/pingxingyun/components/workOrder/orderList.vue')['default']
    PingxingyunComponentsWorkOrderOrderNum: typeof import('./src/components/pingxingyun/components/workOrder/orderNum.vue')['default']
    PingxingyunComponentsWorkOrderOrderRoad: typeof import('./src/components/pingxingyun/components/workOrder/orderRoad.vue')['default']
    PingxingyunComponentsWorkOrderOrderStatus: typeof import('./src/components/pingxingyun/components/workOrder/orderStatus.vue')['default']
    PingxingyunComponentsWorkOrderOrderTitle: typeof import('./src/components/pingxingyun/components/workOrder/orderTitle.vue')['default']
    PingxingyunComponentsWorkOrderOrderType: typeof import('./src/components/pingxingyun/components/workOrder/orderType.vue')['default']
    PingxingyunComponentsYunChart: typeof import('./src/components/pingxingyun/components/yunChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./src/components/Screenfull/index.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon/index.vue')['default']
  }
}
